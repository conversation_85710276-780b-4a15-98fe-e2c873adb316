package main

import (
	"context"
	"fmt"
	"image"
	"image/png"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	json "github.com/goccy/go-json"
	"go.uber.org/zap"

	"MagneticOperator/app/models"
	"MagneticOperator/app/services"
	"MagneticOperator/app/utils"

	"github.com/kbinani/screenshot"
	"github.com/valyala/fasthttp"
	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// 删除RoundStatus结构体，使用简化的截图管理

// ToastNotificationData 通知数据结构
type ToastNotificationData struct {
	Title        string `json:"title"`
	Message      string `json:"message"`
	Type         string `json:"type"`         // success, error, warning, info
	Duration     int    `json:"duration"`     // 显示时长(毫秒)
	ShowProgress bool   `json:"showProgress"` // 是否显示进度条
	Progress     int    `json:"progress"`     // 进度百分比
}

// OCRTaskContext OCR任务上下文，用于追踪每个OCR任务的唯一性
type OCRTaskContext struct {
	TaskID    string              `json:"task_id"`          // 唯一任务ID
	UserName  string              `json:"user_name"`        // 用户名
	Mode      string              `json:"mode"`             // 模式 (B02/C03)
	ImagePath string              `json:"image_path"`       // 图片路径
	StartTime time.Time           `json:"start_time"`       // 开始时间
	Completed bool                `json:"completed"`        // 是否完成
	Result    *services.OCRResult `json:"result,omitempty"` // OCR结果
	Error     error               `json:"error,omitempty"`  // 错误信息
}

// App struct
type App struct {
	ctx context.Context

	// 服务实例
	configService     *services.ConfigService
	screenshotService *services.ScreenshotService
	qrcodeService     *services.QRCodeService
	apiService        *services.APIService
	patientService    *services.PatientService
	hotkeyService     *services.HotkeyService
	ocrService        services.OCRInterface
	// 简化的截图管理器
	simpleScreenshotManager *services.SimpleScreenshotManager
	// simpleScreenshotAPI 已删除 - 使用简化的截图管理

	// 失败任务管理器
	failedTaskManager *services.FailedTaskManager

	// 任务管理器
	taskManager *services.TaskManager

	// 当前配置
	config *models.AppConfig

	// 窗体状态
	isExpanded     bool
	windowPosition string // "left" 或 "right"

	// 用户检测信息映射 - 用于10轮检测数据收集
	currentUserCheckingInfo map[string]*models.CurrentUserCheckingInfo
	checkingInfoMutex       sync.RWMutex // 保护用户检测信息的并发访问

	// OCR任务追踪 - 用于防止并发冲突
	ocrTaskContexts map[string]*OCRTaskContext // key: taskID, value: 任务上下文
	ocrTaskMutex    sync.RWMutex               // 保护OCR任务上下文的并发访问

	// 当前患者状态管理
	currentPatientIndex int          // 当前选中的候检者索引
	currentPatientName  string       // 当前患者姓名（用于历史患者查看场景）
	patientMu           sync.RWMutex // 患者状态锁
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{
		currentUserCheckingInfo: make(map[string]*models.CurrentUserCheckingInfo),
		ocrTaskContexts:         make(map[string]*OCRTaskContext),
	}
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	// 只在调试模式下输出启动信息
	if os.Getenv("DEBUG") == "true" {
		fmt.Println("=== 应用开始启动 ===")
	}
	utils.LogInfo("=== STARTUP函数被调用 ===")
	a.ctx = ctx

	// 初始化日志
	if err := utils.InitLogger(); err != nil {
		fmt.Printf("警告: 初始化日志失败: %v\n", err)
		return
	}

	if os.Getenv("DEBUG") == "true" {
		fmt.Println("日志系统初始化成功")
	}
	utils.LogInfo("日志系统初始化成功")

	// 初始化消息配置
	if err := utils.InitMessages(); err != nil {
		fmt.Printf("警告: 初始化消息配置失败: %v\n", err)
		utils.LogError("初始化消息配置失败", "error", err)
	} else {
		if os.Getenv("DEBUG") == "true" {
			fmt.Println("消息配置系统初始化成功")
		}
		utils.LogInfo("消息配置系统初始化成功")
	}

	// 只在调试模式下输出详细信息
	if os.Getenv("DEBUG") == "true" {
		fmt.Println("开始初始化服务...")
		fmt.Println("调用 initServices()...")
	}
	utils.LogInfo("开始初始化服务...")
	utils.LogInfo("开始调用 initServices()...")
	a.initServices()
	if os.Getenv("DEBUG") == "true" {
		fmt.Println("initServices() 完成")
	}
	utils.LogInfo("initServices() 完成")

	// 简化的截图管理器已在initServices中初始化
	utils.LogInfo("简化截图管理器已就绪")

	// 初始化窗体状态
	a.isExpanded = false
	a.windowPosition = "left" // 默认左侧
	utils.LogInfo("窗体状态初始化完成")

	// 创建必要的目录
	utils.LogInfo("开始创建必要的目录...")
	if err := os.MkdirAll("pic", 0755); err != nil {
		fmt.Printf("创建pic目录失败: %v\n", err)
		utils.LogError("创建pic目录失败", "", err)
	} else {
		utils.LogInfo("pic目录创建成功")
	}
	if err := os.MkdirAll("config", 0755); err != nil {
		fmt.Printf("创建config目录失败: %v\n", err)
		utils.LogError("创建config目录失败", "", err)
	} else {
		utils.LogInfo("config目录创建成功")
	}

	// 启动时检测OCR环境
	utils.LogInfo("开始检测OCR环境...")
	a.checkOCREnvironmentOnStartup()

	// 启动快捷键监听
	utils.LogInfo("开始启动快捷键监听...")
	if a.hotkeyService != nil {
		a.hotkeyService.StartHotkeyListener()
		utils.LogInfo("快捷键服务启动成功")
	} else {
		utils.LogWarning("快捷键服务未初始化")
	}

	// 启动OCR任务清理定时器
	utils.LogInfo("启动OCR任务清理定时器...")
	go func() {
		ticker := time.NewTicker(30 * time.Minute) // 每30分钟清理一次
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				a.cleanupOldOCRTasks()
			case <-ctx.Done():
				fmt.Printf("[OCR-TASK] OCR任务清理定时器停止\n")
				return
			}
		}
	}()
	utils.LogInfo("OCR任务清理定时器启动成功")

	fmt.Println("=== 应用启动成功 ===")
	utils.LogInfo("应用启动成功")
}

// domReady 在DOM准备就绪时调用，设置窗口位置和尺寸
func (a *App) domReady(ctx context.Context) {
	a.ctx = ctx
	utils.LogInfo("DOM准备就绪，开始设置窗口位置和尺寸")

	// 设置窗口位置和尺寸
	a.setWindowToSide()

	// 根据配置设置窗体置顶
	if a.config != nil && a.config.NormalWindowsSetting.AlwaysOnTop {
		runtime.WindowSetAlwaysOnTop(ctx, true)
		utils.LogInfo("窗体已设置为置顶")
	} else {
		utils.LogInfo("窗体置顶功能已禁用")
	}

	// 显示窗体
	runtime.WindowShow(ctx)
	utils.LogInfo("窗体已显示")
}

// initServices 初始化所有服务
func (a *App) initServices() {
	utils.LogInfo("开始初始化服务...")

	// 创建配置服务
	a.configService = services.NewConfigService(a.ctx)

	// 加载配置
	config, err := a.configService.LoadConfig()
	if err != nil {
		utils.LogError("加载配置失败", "", err)
		// 使用默认配置
		a.config = &models.AppConfig{}
		a.setDefaultConfig()
		// 确保ConfigService也有配置引用
		a.configService.SetConfig(a.config)
		utils.LogInfo("已加载默认配置")
	} else {
		a.config = config
		utils.LogInfo("成功加载配置文件")
	}

	// 创建其他服务
	a.screenshotService = services.NewScreenshotService(a.configService)
	// 设置患者信息回调函数
	a.screenshotService.SetPatientInfoCallback(func() (string, string) {
		return a.getCurrentPatientName(), a.getCurrentPatientRegistrationNumber()
	})
	// 设置失败任务回调函数
	a.screenshotService.SetFailedTaskCallback(func(userName, mode, imagePath, errorMsg string) {
		if a.failedTaskManager != nil {
			a.failedTaskManager.AddFailedTask(userName, mode, imagePath, errorMsg)
		}
	})
	a.qrcodeService = services.NewQRCodeService(a.configService)
	a.apiService = services.NewAPIService(a.configService)
	a.patientService = services.NewPatientService(a.configService, a.apiService)
	a.hotkeyService = services.NewHotkeyService(a)
	a.ocrService = services.NewOCRService(a.configService, a)

	// 根据配置选择OCR处理器类型
	var ocrProcessor services.OCRProcessorInterface
	if a.config.Concurrency.SerialMode {
		// 创建串行OCR处理器
		serialProcessor, err := services.NewSerialOCRProcessor(a.ocrService, a.configService)
		if err != nil {
			utils.LogError("创建串行OCR处理器失败", "", err)
			return // 如果创建失败，直接返回
		} else {
			utils.LogInfo("已启用串行OCR处理模式，确保API请求按顺序执行")
			fmt.Printf("[系统] 串行模式已启用 - 单台设备20次截图将按顺序处理，预计耗时约14分钟\n")
			ocrProcessor = serialProcessor
		}
	} else {
		// 创建并发OCR处理器
		concurrentProcessor, err := services.NewConcurrentOCRProcessor(a.ocrService, a.configService, 10) // 10 is the default value
		if err != nil {
			utils.LogError("创建并发OCR处理器失败", "", err)
			return // 如果创建失败，直接返回
		} else {
			utils.LogInfo("已启用并发OCR处理模式")
			ocrProcessor = concurrentProcessor
		}
	}

	// 创建失败任务管理器
	a.failedTaskManager = services.NewFailedTaskManager(a.ocrService, a.screenshotService, a)
	if err := a.failedTaskManager.Start(); err != nil {
		utils.LogError("启动失败任务管理器失败", "", err)
	} else {
		utils.LogInfo("失败任务管理器启动成功")
	}

	// 创建简化的截图管理器
	a.simpleScreenshotManager = services.NewSimpleScreenshotManager(ocrProcessor, a.screenshotService, a)
	if err := a.simpleScreenshotManager.Start("default_user"); err != nil {
		utils.LogError("启动简化截图管理器失败", "", err)
	} else {
		utils.LogInfo("简化截图管理器启动成功")
	}

	// 创建简化的截图API
	// simpleScreenshotAPI 已删除 - 使用简化的截图管理
	utils.LogInfo("简化截图API创建成功")

	// 初始化任务管理器
	fmt.Println("开始初始化任务管理器...")
	utils.LogInfo("开始初始化任务管理器...")
	a.initTaskManager()
	fmt.Println("任务管理器初始化完成")
	utils.LogInfo("任务管理器初始化成功")

	// 设置API服务到配置服务，用于后续加载站点信息
	a.configService.SetAPIService(a.apiService)

	// 从API加载站点信息并检查是否有变化
	utils.LogInfo("开始从API加载站点信息...")
	if err := a.configService.LoadSiteInfoFromAPIWithCache(false); err != nil {
		utils.LogError("从API加载站点信息失败", "", err)
		// 这里可以根据需要处理错误，例如使用本地配置的站点信息或提示用户
	} else {
		// 站点信息加载成功后，重新获取更新后的配置
		a.config = a.configService.GetConfig()
		if a.configService.IsSiteInfoChanged() {
			utils.LogInfo("站点信息有变化，将重新生成二维码")
			fmt.Printf("站点信息更新成功: %s (%s)\n", a.config.SiteInfo.SiteName, a.config.SiteInfo.SiteID)
		} else {
			utils.LogInfo("站点信息无变化，复用现有二维码")
			fmt.Printf("站点信息无变化: %s (%s)\n", a.config.SiteInfo.SiteName, a.config.SiteInfo.SiteID)
		}
	}
}

// initTaskManager 初始化任务管理器
func (a *App) initTaskManager() {
	fmt.Println("创建事件发射器...")
	// 创建事件发射器
	eventEmitter := services.NewWailsEventEmitter(a.ctx)

	fmt.Println("设置App通知器...")
	// 设置App通知器
	eventEmitter.SetAppNotifier(a)

	fmt.Println("创建任务管理器配置...")
	// 创建任务管理器配置
	config := services.DefaultTaskManagerConfig()

	fmt.Println("创建任务管理器...")
	// 创建任务管理器
	a.taskManager = services.NewTaskManager(config, eventEmitter)

	// 注册任务处理器
	fmt.Println("创建任务处理器...")
	utils.LogInfo("开始创建任务处理器...")

	// 截图任务处理器
	screenshotHandlerA := services.NewScreenshotTaskHandler(a.screenshotService, a.apiService, a, services.TaskTypeScreenshotA)
	screenshotHandlerB := services.NewScreenshotTaskHandler(a.screenshotService, a.apiService, a, services.TaskTypeScreenshotB)
	screenshotHandlerC := services.NewScreenshotTaskHandler(a.screenshotService, a.apiService, a, services.TaskTypeScreenshotC)
	utils.LogInfo("截图任务处理器创建完成")

	// OCR任务处理器
	ocrHandler := services.NewOCRTaskHandler(a.ocrService)
	utils.LogInfo("OCR任务处理器创建完成")

	// 上传任务处理器
	uploadHandler := services.NewUploadTaskHandler(a.apiService)
	utils.LogInfo("上传任务处理器创建完成")

	// 注册处理器
	fmt.Println("注册任务处理器...")
	utils.LogInfo("开始注册任务处理器...")

	if err := a.taskManager.RegisterHandler(screenshotHandlerA); err != nil {
		utils.LogError("注册截图A任务处理器失败", "", err)
	} else {
		utils.LogInfo("截图A任务处理器注册成功")
	}
	if err := a.taskManager.RegisterHandler(screenshotHandlerB); err != nil {
		utils.LogError("注册截图B任务处理器失败", "", err)
	} else {
		utils.LogInfo("截图B任务处理器注册成功")
	}
	if err := a.taskManager.RegisterHandler(screenshotHandlerC); err != nil {
		utils.LogError("注册截图C任务处理器失败", "", err)
	} else {
		utils.LogInfo("截图C任务处理器注册成功")
	}
	if err := a.taskManager.RegisterHandler(ocrHandler); err != nil {
		utils.LogError("注册OCR任务处理器失败", "", err)
	} else {
		utils.LogInfo("OCR任务处理器注册成功")
	}
	if err := a.taskManager.RegisterHandler(uploadHandler); err != nil {
		utils.LogError("注册上传任务处理器失败", "", err)
	} else {
		utils.LogInfo("上传任务处理器注册成功")
	}

	// 启动任务管理器
	fmt.Println("启动任务管理器...")
	utils.LogInfo("开始启动任务管理器...")
	if err := a.taskManager.Start(); err != nil {
		fmt.Printf("启动任务管理器失败: %v\n", err)
		utils.LogError("启动任务管理器失败", "", err)
	} else {
		fmt.Println("任务管理器启动成功！")
		utils.LogInfo("任务管理器启动成功")
	}

}

// SubmitScreenshotTask 提交截图任务到任务管理器
func (a *App) SubmitScreenshotTask(taskType services.TaskType, mode, description string) {
	fmt.Printf("=== 收到截图任务请求 ===\n")
	fmt.Printf("任务类型: %s, 模式: %s, 描述: %s\n", taskType, mode, description)
	utils.LogInfo(fmt.Sprintf("收到截图任务请求: %s (模式: %s, 任务类型: %s)", description, mode, taskType))

	if a.taskManager == nil {
		fmt.Println("警告: 任务管理器为nil，回退到传统截图方式")
		utils.LogWarning("任务管理器为nil，回退到传统截图方式")
		go func() {
			_, err := a.ProcessScreenshotWorkflow(mode)
			if err != nil {
				utils.LogError(fmt.Sprintf("%s失败", description), a.getCurrentPatientName(), err)
			}
		}()
		return
	}

	if !a.taskManager.IsRunning() {
		fmt.Println("警告: 任务管理器未运行，回退到传统截图方式")
		utils.LogWarning("任务管理器未运行，回退到传统截图方式")
		go func() {
			_, err := a.ProcessScreenshotWorkflow(mode)
			if err != nil {
				utils.LogError(fmt.Sprintf("%s失败", description), a.getCurrentPatientName(), err)
			}
		}()
		return
	}

	fmt.Println("任务管理器可用，提交任务到队列")
	utils.LogInfo("任务管理器可用，提交任务到队列")

	userName := a.getCurrentPatientName()

	// 提交任务到任务管理器
	task, err := a.taskManager.SubmitTask(
		taskType,
		mode,
		userName,

		services.PriorityNormal,
		map[string]interface{}{
			"description": description,
			"timestamp":   time.Now().Format("2006-01-02 15:04:05"),
		},
	)

	if err != nil {
		utils.LogError(fmt.Sprintf("提交%s任务失败", description), userName, err)

		// 如果是冷却期错误，给用户友好提示
		if strings.Contains(err.Error(), "cooldown period") {
			// 发送前端通知
			runtime.EventsEmit(a.ctx, "showToastNotification", map[string]interface{}{
				"type":    "warning",
				"title":   "操作过于频繁",
				"message": fmt.Sprintf("%s正在处理中，请稍后再试", description),
			})
		} else if strings.Contains(err.Error(), "already running") {
			// 发送前端通知
			runtime.EventsEmit(a.ctx, "showToastNotification", map[string]interface{}{
				"type":    "info",
				"title":   "任务进行中",
				"message": fmt.Sprintf("%s任务正在执行，请等待完成", description),
			})
		} else {
			// 其他错误，回退到传统方式
			utils.LogWarning("任务提交失败，回退到传统截图方式")
			go func() {
				_, err := a.ProcessScreenshotWorkflow(mode)
				if err != nil {
					utils.LogError(fmt.Sprintf("%s失败", description), userName, err)
				}
			}()
		}
		return
	}

	utils.LogInfo(fmt.Sprintf("成功提交%s任务 - TaskID: %s, User: %s",
		description, task.ID, userName))

	// 发送前端通知
	runtime.EventsEmit(a.ctx, "showToastNotification", map[string]interface{}{
		"type":    "info",
		"title":   "任务已提交",
		"message": fmt.Sprintf("%s任务已加入队列，正在处理...", description),
	})
}

// SendTaskCompletedNotification 发送任务完成通知（实现AppNotifier接口）
func (a *App) SendTaskCompletedNotification(taskResult *services.TaskResult) {
	fmt.Printf("=== 收到任务完成通知 ===\n")
	if taskResult != nil {
		fmt.Printf("任务ID: %s, 状态: %s\n", taskResult.TaskID, taskResult.Status)
		utils.LogInfo(fmt.Sprintf("收到任务完成通知 - 任务ID: %s, 状态: %s", taskResult.TaskID, taskResult.Status))
	}

	if taskResult == nil {
		utils.LogWarning("任务结果为nil，无法发送通知")
		return
	}

	if a.ctx == nil {
		fmt.Println("警告: 应用上下文为nil，无法发送通知")
		utils.LogWarning("应用上下文为nil，无法发送通知")
		return
	}

	var title, message string
	var notificationType string

	// 根据任务状态确定通知类型和内容
	switch taskResult.Status {
	case services.TaskStatusCompleted:
		notificationType = "success"
		title = "任务完成"
		utils.LogInfo("任务执行成功，准备发送成功通知")

		// 根据任务类型生成具体消息
		switch taskResult.TaskID {
		default:
			if strings.Contains(taskResult.TaskID, "screenshot_a") {
				message = "器官问题来源分析截图已完成"
			} else if strings.Contains(taskResult.TaskID, "screenshot_b") {
				message = "生化平衡分析截图已完成"
			} else if strings.Contains(taskResult.TaskID, "screenshot_c") {
				message = "病理形态学分析截图已完成"
			} else {
				message = "截图任务已完成"
			}
		}

	case services.TaskStatusFailed:
		notificationType = "error"
		title = "任务失败"
		message = fmt.Sprintf("任务执行失败: %s", taskResult.Error)
		utils.LogError("任务执行失败，准备发送失败通知", "", fmt.Errorf(taskResult.Error))

	case services.TaskStatusCancelled:
		notificationType = "warning"
		title = "任务已取消"
		message = "任务已被取消"
		utils.LogInfo("任务被取消，准备发送取消通知")

	default:
		utils.LogInfo(fmt.Sprintf("任务状态 %s 不需要发送通知", taskResult.Status))
		return // 其他状态不发送通知
	}

	// 发送前端通知
	// 只在调试模式下输出Toast通知信息
	if a.config != nil && a.config.Debug {
		fmt.Printf("发送Toast通知: 类型=%s, 标题=%s, 消息=%s\n", notificationType, title, message)
		utils.LogInfo(fmt.Sprintf("准备发送Toast通知 - 类型: %s, 标题: %s, 消息: %s", notificationType, title, message))
	}

	runtime.EventsEmit(a.ctx, "showToastNotification", map[string]interface{}{
		"type":     notificationType,
		"title":    title,
		"message":  message,
		"taskId":   taskResult.TaskID,
		"duration": taskResult.Duration,
	})

	fmt.Println("Toast通知已发送到前端")
	utils.LogInfo(fmt.Sprintf("Toast通知已发送到前端: %s - %s", title, message))
}

// setDefaultConfig 设置默认配置
func (a *App) setDefaultConfig() {
	// 默认配置现在通过app_config.json文件管理
	// 紧凑和展开模式的设置已在配置文件中定义
}

// GetConfig 获取应用配置
func (a *App) GetConfig() *models.AppConfig {
	// 只在调试模式下输出详细日志，避免日志冗余
	if a.config.SiteInfo.SiteID != "" {
		utils.LogInfo(fmt.Sprintf("GetConfig called - SiteInfo: %s (%s)", a.config.SiteInfo.SiteName, a.config.SiteInfo.SiteID))
	} else {
		utils.LogInfo("GetConfig called - SiteInfo not loaded yet")
	}
	return a.config
}

// UpdateSiteInfo 更新网点信息
func (a *App) UpdateSiteInfo(siteInfo models.AppConfig) error {
	// 站点信息现在从API动态加载，不再支持手动更新
	utils.LogOperation("尝试手动更新网点信息，但此功能已改为API自动更新", "", siteInfo.SiteInfo.SiteID)

	// 触发重新从API加载站点信息
	if err := a.configService.LoadSiteInfoFromAPI(); err != nil {
		return fmt.Errorf("从API重新加载站点信息失败: %v", err)
	}

	// 更新当前配置
	a.config = a.configService.GetConfig()
	utils.LogInfo("站点信息已从API重新加载")

	return nil
}

// UpdateCropSettings 更新裁剪设置
func (a *App) UpdateCropSettings(cropSettings models.AppConfig) error {
	utils.LogOperation("更新裁剪设置", "", a.config.SiteInfo.SiteID)
	return a.configService.UpdateCropSettings(cropSettings)
}

// UpdateNotificationMode 更新通知模式
func (a *App) UpdateNotificationMode(useSystemNotification bool) error {
	a.config.UseSystemNotification = useSystemNotification
	return a.configService.UpdateNotificationMode(useSystemNotification)
}

// ProcessImageWithOCR 对预处理后的图片进行OCR识别并返回JSON数据（第二步）
func (a *App) ProcessImageWithOCR(imagePath string) (*services.OCRResult, error) {
	patientName := a.getCurrentPatientName()
	utils.LogOperation("图片OCR识别", patientName, a.config.SiteInfo.SiteID)
	fmt.Printf("[DEBUG] 开始对图片进行OCR API识别: %s\n", imagePath)

	// 调用OCR API进行图片识别，获取完整的JSON响应数据
	ocrResult, err := a.ocrService.ProcessImageWithDetails(context.Background(), imagePath)
	if err != nil {
		utils.LogError("OCR_API调用失败...", patientName, err)
		fmt.Printf("[ERROR] OCR_API调用失败: %v\n", err)

		// 将失败的OCR任务添加到失败任务管理器
		if a.failedTaskManager != nil {
			mode := "OCR" // 默认模式，可以根据需要调整
			a.failedTaskManager.AddFailedTask(patientName, mode, imagePath, err.Error())
			fmt.Printf("[INFO] 已将失败的OCR任务添加到重试队列: %s\n", imagePath)
		}

		return nil, fmt.Errorf("OCR_API调用失败...: %v", err)
	}
	return ocrResult, nil
}

// ProcessScreenshotWorkflow 处理完整的截图工作流程（整合四个步骤）
func (a *App) ProcessScreenshotWorkflow(mode string) (string, error) {
	patientName := a.getCurrentPatientName()

	fmt.Printf("[DEBUG] 开始截图工作流程，模式: %s, 患者: %s\n", mode, patientName)
	utils.LogInfo(fmt.Sprintf("[操作:快捷键截图-模式%s] [当前受检者:%s] [网点:%s]", mode, patientName, a.config.SiteInfo.SiteID))

	// 获取当前选中的用户信息（只获取待检测的患者）
	registrations, err := a.GetPendingRegistrations()
	if err != nil {
		return "", fmt.Errorf("获取当前受检者信息失败: %v", err)
	}

	// 根据当前选中索引获取用户信息
	var currentUser *models.Registration
	if len(registrations) > 0 {
		// 获取当前选中的患者索引
		currentIndex := a.GetCurrentPatientIndex()
		if currentIndex >= 0 && currentIndex < len(registrations) {
			currentUser = &registrations[currentIndex]
		} else {
			// 如果索引无效，使用第一个
			currentUser = &registrations[0]
		}
	} else {
		utils.LogOperation("当前没有选中受检者，处于本系统操作者自行研究模式", "暂无候检者", a.config.SiteInfo.SiteID)
		fmt.Printf("[INFO] 当前没有选中受检者，处于本系统操作者自行研究模式...\n")
	}

	utils.LogOperation("处理截图工作流程", patientName, a.config.SiteInfo.SiteID)
	utils.LogInfo(fmt.Sprintf("开始处理截图工作流程 - 模式: %s, 用户: %s\n", mode, patientName))
	fmt.Printf("[DEBUG] 开始处理截图工作流程 - 模式: %s, 用户: %s\n", mode, patientName)

	// 第一步：TakeScreenshotWithPreprocessing截取完整屏幕截图并进行预处理
	fmt.Printf("[DEBUG] 第一步: 截取完整屏幕截图并进行预处理...\n")
	tempFilePath, err := a.screenshotService.TakeScreenshotWithPreprocessing(mode, patientName)
	if err != nil {
		utils.LogError("截取屏幕截图失败", patientName, err)
		fmt.Printf("[ERROR] 第一步失败: 截取屏幕截图失败 - %v\n", err)
		return "", fmt.Errorf("截取屏幕截图失败: %v", err)
	}
	fmt.Printf("[DEBUG] 第一步成功: 预处理截图已保存到 %s\n", tempFilePath)

	// 第二步：ProcessImageWithOCR对预处理后的图片进行OCR识别（线程安全版本）
	fmt.Printf("[DEBUG] 第二步: 对预处理后的图片进行OCR API调用和识别...\n")

	// 获取当前轮次用于任务追踪（已在函数开头声明）
	fmt.Printf("[截图工作流] 简化管理模式, 患者: %s, 模式: %s\n", patientName, mode)

	// 检查是否为重复的OCR任务
	if a.isOCRTaskDuplicate(patientName, mode, tempFilePath) {
		utils.LogInfo("检测到重复的OCR任务，跳过处理", zap.String("用户", patientName), zap.String("模式", mode))
		return "", fmt.Errorf("重复的OCR任务，已跳过处理")
	}

	// 创建OCR任务上下文
	ocrTaskContext := a.createOCRTaskContext(patientName, mode, tempFilePath)
	fmt.Printf("[截图工作流] 创建OCR任务上下文: %s\n", ocrTaskContext.TaskID)

	// 执行OCR识别
	ocrResult, err := a.ProcessImageWithOCR(tempFilePath)

	// 完成OCR任务上下文
	a.completeOCRTaskContext(ocrTaskContext.TaskID, ocrResult, err)

	if err != nil {
		fmt.Printf("[ERROR] 第二步失败: OCR API调用和识别失败 - %v\n", err)
		// 确保任务管理器中的任务状态也被正确更新为失败
		a.notifyTaskManagerCompletion(ocrTaskContext.TaskID, false, err)
		return "", fmt.Errorf("OCR_API调用和识别失败: %v", err)
	}
	fmt.Printf("[DEBUG] 第二步成功: OCR API调用和识别完成 - 任务ID: %s\n", ocrTaskContext.TaskID)

	// 通知任务管理器任务成功完成
	a.notifyTaskManagerCompletion(ocrTaskContext.TaskID, true, nil)

	// 打印OCR API调用返回值信息
	fmt.Printf("[DEBUG] OCR API返回值详情:\n")
	fmt.Printf("[DEBUG] - 校验后器官名称: %s\n", ocrResult.OrganName)
	fmt.Printf("[DEBUG] - 键值对数量: %d\n", len(ocrResult.KeyValuePairs))
	fmt.Printf("[DEBUG] - 置信度: %.2f\n", ocrResult.Confidence)
	fmt.Printf("[DEBUG] - 图片路径: %s\n", ocrResult.ImagePath)
	// fmt.Printf("[DEBUG] - 原始响应长度: %d 字符\n", len(ocrResult.RawResponse))
	utils.LogInfo(fmt.Sprintf("OCR API返回值详情 - 校验后器官名称: %s, 键值对数量: %d, 置信度: %.2f, 图片路径: %s\n", ocrResult.OrganName, len(ocrResult.KeyValuePairs), ocrResult.Confidence, ocrResult.ImagePath))

	// 安全地输出原始响应信息
	responseLength := len(ocrResult.RawResponse)
	fmt.Printf("[DEBUG] - 原始响应长度: %d 字节\n", responseLength)

	if responseLength > 500 {
		fmt.Printf("[DEBUG] - 原始响应预览: %s...\n", string(ocrResult.RawResponse[:500]))
	} else if responseLength > 0 {
		fmt.Printf("[DEBUG] - 原始响应完整内容: %s\n", string(ocrResult.RawResponse))
	} else {
		fmt.Printf("[DEBUG] - 原始响应为空\n")
	}

	fmt.Printf("[DEBUG] 第二步完成，准备进入第三步...\n")

	// - ProcessImageWithOCR 调用的 ProcessImageWithDetails 已经完成了器官名称的提取和标准化校验
	// - ocrResult.OrganName 已经包含了经过校验的标准器官名称
	// // 第三步：从OCR响应JSON数据中提取器官名称并弹窗提示
	// fmt.Printf("[DEBUG] 第三步: 从OCR响应JSON数据中提取器官名称并弹窗提示...\n")
	// organName, err := a.screenshotService.ExtractOrganNameFromOCRResponse(ocrResult)
	// if err != nil {
	// 	fmt.Printf("[DEBUG] 第三步: 器官名称提取失败，使用默认器官名称 - %v\n", err)
	// 	organName = "未知器官"
	// } else {
	// 	fmt.Printf("[DEBUG] 第三步成功: 器官名称 - %s\n", organName)
	// }

	// 第三步：根据截图模式决定是否调用颜色检测
	// 只对B02模式（生化平衡分析）进行颜色识别，C03模式（病理形态学分析）跳过
	if mode == "B02" || strings.Contains(tempFilePath, "_B_") {
		fmt.Printf("[DEBUG] 第三步: 检测到B02模式（生化平衡分析），开始调用颜色检测算法进行文字颜色分析...\n")
		// 直接使用已经校验过的OCR数据，无需重复更新器官名称
		// colorResult, err := services.ProcessImageColorDetection(tempFilePath, ocrResult.RawResponse, a.config)
		_, err := services.ProcessImageColorDetection(tempFilePath, ocrResult.RawResponse, a.config)

		if err != nil {
			fmt.Printf("[WARNING] 颜色检测失败: %v\n", err)
			// 记录颜色检测失败到日志
			utils.LogWarning("颜色检测失败", zap.String("tempFilePath", tempFilePath), zap.Error(err))
		} else {
			fmt.Printf("[DEBUG] 颜色检测成功完成\n")
			// 记录颜色检测成功到日志
			utils.LogInfo("颜色检测成功完成", zap.String("tempFilePath", tempFilePath))
			// if a.config.ColorDetection.DebugMode {
			// 	fmt.Printf("[DEBUG] 颜色检测结果: %+v\n", colorResult)
			// }
		}
		fmt.Printf("[DEBUG] 第三步完成，准备进入第四步...\n")
	} else {
		fmt.Printf("[DEBUG] 第三步: 检测到C03模式（病理形态学分析），跳过颜色检测\n")
	}

	fmt.Printf("[DEBUG] 第三步结束，即将进入第四步\n")

	// 注释掉错误的模拟进度通知，真正的Toast通知在screenshot_round_manager.go中处理
	// a.showProcessingNotification(organName)

	// 第四步：提取D值列表数据并装入currentUserCheckingInfoJSON
	fmt.Printf("[DEBUG] 第四步开始: 提取D值列表数据并更新用户检测信息...\n")
	utils.LogInfo("开始第四步：提取D值列表数据", zap.String("patientName", patientName), zap.String("mode", mode))

	dHealthTrend, err := a.screenshotService.ExtractHealthTrendDataFromOCR(ocrResult)
	if err != nil {
		fmt.Printf("[WARNING] 第四步: 提取D值列表失败 - %v\n", err)
		utils.LogWarning("提取D值列表失败", zap.Error(err))
		dHealthTrend = make(map[string]string) // 使用空的map作为默认值
	} else {
		fmt.Printf("[DEBUG] 第四步成功: 提取到 %d 个D值数据\n", len(dHealthTrend))
		utils.LogInfo("提取D值列表成功", zap.Int("dataCount", len(dHealthTrend)))
	}

	fmt.Printf("[DEBUG] 第四步完成，准备更新用户检测信息...\n")

	// 更新currentUserCheckingInfoJSON
	err = a.updateCurrentUserCheckingInfo(patientName, mode, tempFilePath, ocrResult.OrganName, ocrResult, dHealthTrend, currentUser)
	if err != nil {
		fmt.Printf("[WARNING] 更新用户检测信息失败: %v\n", err)
	}

	// 打印当前用户检测信息状态（调试用）
	a.printCurrentUserCheckingInfoStatus(patientName, "截图完成后")

	// 保存最终文件
	finalFilePath, jsonFilePath, err := a.screenshotService.SaveFinalScreenshotWithData(tempFilePath, mode, patientName, ocrResult.OrganName, currentUser, dHealthTrend)
	if err != nil {
		utils.LogError("保存最终文件失败", patientName, err)
		fmt.Printf("[ERROR] 保存最终文件失败 - %v\n", err)
		return "", fmt.Errorf("保存最终文件失败: %v", err)
	}
	fmt.Printf("[DEBUG] 最终文件已保存到 %s, JSON数据已保存到 %s\n", finalFilePath, jsonFilePath)

	// 使用简化的截图管理 - 检查是否完成20个任务
	if a.simpleScreenshotManager != nil && a.simpleScreenshotManager.IsCompleted() {
		fmt.Printf("[INFO] 已完成20个并发截图任务，当前受检者/患者 %s 健康分析结束\n", patientName)
		fmt.Printf("[DEBUG] 所有截图任务已完成，准备调用扣子API进行分析...\n")
		go a.HandleCozeLLM_AnylizeD_value(patientName) // 异步调用扣子API

		// 标记当前患者完成状态并自动切换到下一位患者
		fmt.Printf("[INFO] 标记当前患者完成状态并准备切换到下一位候检者...\n")
		go func() {
			// 延迟一秒后处理，确保当前操作完成
			time.Sleep(1 * time.Second)

			// 标记当前患者完成状态
			if currentUser != nil {
				if err := a.MarkPatientCompleted(currentUser.UserID, currentUser.RegistrationNumber); err != nil {
					fmt.Printf("[ERROR] 标记患者完成状态失败: %v\n", err)
				} else {
					fmt.Printf("[INFO] 已标记患者 %s 完成检测状态\n", patientName)
				}
			}

			// 自动切换到下一位患者
			if err := a.MoveToNextPatient(); err != nil {
				fmt.Printf("[ERROR] 自动切换到下一位患者失败: %v\n", err)
			} else {
				fmt.Printf("[INFO] 已自动切换到下一位患者\n")
			}
		}()
	} else {
		fmt.Printf("[DEBUG] 截图任务进行中，等待完成更多任务\n")
	}

	fmt.Printf("[DEBUG] 截图工作流程完成，最终文件: %s\n", finalFilePath)
	return finalFilePath, nil
}

// updateCurrentUserCheckingInfo 更新用户检测信息JSON（线程安全版本）
func (a *App) updateCurrentUserCheckingInfo(userName, mode, imagePath, organName string, ocrResult *services.OCRResult, dHealthTrend map[string]string, currentUser *models.Registration) error {
	// 创建唯一的操作ID用于日志追踪
	operationID := fmt.Sprintf("%s_%s_%d", userName, mode, time.Now().UnixNano())
	utils.LogInfo("开始更新用户检测信息变量", zap.String("operationID", operationID))

	// 记录到日志文件
	utils.LogInfo("开始更新用户检测信息变量",
		zap.String("userName", userName),
		zap.String("mode", mode),
		zap.String("imagePath", imagePath),
		zap.String("organName", organName),
		zap.String("operationID", operationID),
	)

	a.checkingInfoMutex.Lock()
	defer a.checkingInfoMutex.Unlock()

	// 获取或创建用户检测信息
	userInfo, exists := a.currentUserCheckingInfo[userName]
	if !exists {
		userID := ""
		baodaoNumber := ""
		if currentUser != nil {
			userID = currentUser.UserID
			baodaoNumber = currentUser.RegistrationNumber
		}
		userInfo = models.NewCurrentUserCheckingInfo(userID, userName, a.config.SiteInfo.SiteID, baodaoNumber)
		a.currentUserCheckingInfo[userName] = userInfo
		utils.LogInfo("创建新用户检测信息", zap.String("operationID", operationID), zap.String("用户", userName))
	}

	// 获取下一个可用的任务索引
	taskIndex := userInfo.GetNextTaskIndex()
	if taskIndex == -1 {
		return fmt.Errorf("所有20个截图任务已完成，无法添加更多任务")
	}

	utils.LogInfo("分配任务索引", zap.Int("taskIndex", taskIndex), zap.String("operationID", operationID))

	// 创建新的截图任务
	task := &models.ScreenshotTask{
		TaskID:      strconv.Itoa(taskIndex),
		Mode:        mode,
		OrganName:   organName,
		InputImage:  imagePath,
		Status:      "completed",
		CreatedTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	// 从OCR结果中提取数据
	if len(ocrResult.RawResponse) > 0 {
		var rawMap map[string]interface{}
		if err := json.Unmarshal(ocrResult.RawResponse, &rawMap); err == nil {
			if recTexts, exists := rawMap["rec_texts"]; exists {
				if textsArray, ok := recTexts.([]interface{}); ok {
					task.RecTexts = make([]string, len(textsArray))
					for i, text := range textsArray {
						if str, ok := text.(string); ok {
							task.RecTexts[i] = str
						}
					}
				}
			}
			if recPolys, exists := rawMap["rec_polys"]; exists {
				if polysArray, ok := recPolys.([]interface{}); ok {
					task.RecPolys = polysArray
				}
			}
		}
	}

	// 根据模式处理分析数据
	if mode == "B" || mode == "B02" || mode == "器官问题来源分析" {
		// 处理生化分析数据
		for dValue, text := range dHealthTrend {
			analysis := models.BiochemicalAnalysis{
				DValue:     dValue,
				Text:       text,
				FinalColor: "", // 颜色信息需要从其他地方获取
			}
			task.BiochemicalAnalysis = append(task.BiochemicalAnalysis, analysis)
		}
		fmt.Printf("[DEBUG] B02模式任务处理完成: %s\n", userName)
	} else if mode == "C" || mode == "C03" || mode == "病理形态学分析" {
		// 处理病理分析数据
		for dValue, text := range dHealthTrend {
			analysis := models.PathologyAnalysis{
				DValue: dValue,
				Text:   text,
			}
			task.PathologyAnalysis = append(task.PathologyAnalysis, analysis)
		}
		utils.LogInfo("C03模式任务处理完成", zap.String("operationID", operationID))
		fmt.Printf("[DEBUG] C03模式任务处理完成: %s\n", userName)
	}

	// 添加任务到用户检测信息
	userInfo.AddScreenshotTask(*task)

	// 更新器官统计
	if organName != "" && organName != "未知器官" {
		userInfo.DetectedOrgans[organName]++
		utils.LogInfo("更新器官统计", zap.String("器官", organName), zap.String("operationID", operationID))
	}

	fmt.Printf("[DEBUG] 更新用户检测信息: 用户=%s, 模式=%s, 器官=%s", userName, mode, organName)

	// 记录数据更新到日志文件
	utils.LogInfo("用户数据更新",
		zap.String("userName", userName),
		zap.String("mode", mode),
		zap.String("organName", organName),
		zap.Int("totalTasks", userInfo.TotalTasks),
		zap.Int("completedTasks", userInfo.CompletedTasks),
		zap.String("operationID", operationID),
	)

	// 准备调试上下文信息（在锁内准备，锁外调用）
	context := fmt.Sprintf("更新完成-%s模式-操作ID:%s", mode, operationID)

	// 释放锁后再调用打印函数，避免死锁
	defer func() {
		a.printCurrentUserCheckingInfoStatus(userName, context)
		utils.LogInfo("用户检测信息变量更新完全完成", zap.String("operationID", operationID))
	}()

	return nil
}

// GetCurrentUserCheckingInfo 获取当前用户的检测信息JSON
func (a *App) GetCurrentUserCheckingInfo(userName string) (*models.CurrentUserCheckingInfo, error) {
	a.checkingInfoMutex.RLock()
	defer a.checkingInfoMutex.RUnlock()

	userInfo, exists := a.currentUserCheckingInfo[userName]
	if !exists {
		return nil, fmt.Errorf("用户 %s 的检测信息不存在", userName)
	}

	return userInfo, nil
}

// ExportCurrentUserCheckingInfoJSON 导出用户检测信息为JSON格式
func (a *App) ExportCurrentUserCheckingInfoJSON(userName string) ([]byte, error) {
	userInfo, err := a.GetCurrentUserCheckingInfo(userName)
	if err != nil {
		return nil, err
	}

	jsonData, err := json.MarshalIndent(userInfo, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("序列化用户检测信息失败: %v", err)
	}

	return jsonData, nil
}

// DiagnoseSystemStatus 诊断系统状态（调试用）
func (a *App) DiagnoseSystemStatus() map[string]interface{} {
	result := make(map[string]interface{})

	// 1. 检查当前患者信息
	currentPatient := a.getCurrentPatientName()
	result["current_patient"] = currentPatient
	result["current_patient_index"] = a.currentPatientIndex

	// 2. 简化的截图管理状态
	result["screenshot_manager"] = "简化管理模式 - 20个并发任务"

	// 3. 检查用户检测信息
	a.checkingInfoMutex.RLock()
	userInfoStatus := make(map[string]interface{})
	for userName, userInfo := range a.currentUserCheckingInfo {
		userInfoStatus[userName] = map[string]interface{}{
			"total_tasks":      userInfo.TotalTasks,
			"completed_tasks":  userInfo.CompletedTasks,
			"screenshot_tasks": len(userInfo.ScreenshotTasks),
			"detected_organs":  userInfo.DetectedOrgans,
		}
	}
	a.checkingInfoMutex.RUnlock()
	result["user_checking_info"] = userInfoStatus

	// 4. 检查OCR任务上下文
	a.ocrTaskMutex.RLock()
	result["ocr_task_contexts_count"] = len(a.ocrTaskContexts)
	ocrTaskSummary := make(map[string]int)
	for _, context := range a.ocrTaskContexts {
		key := fmt.Sprintf("%s_%s", context.UserName, context.Mode)
		ocrTaskSummary[key]++
	}
	a.ocrTaskMutex.RUnlock()
	result["ocr_task_summary"] = ocrTaskSummary

	// 5. 检查报到信息
	registrations, err := a.GetRegistrations(a.config.SiteInfo.SiteID)
	if err != nil {
		result["registrations_error"] = err.Error()
	} else {
		result["registrations_count"] = len(registrations)
		if len(registrations) > 0 && a.currentPatientIndex < len(registrations) {
			result["current_registration"] = registrations[a.currentPatientIndex]
		}
	}

	return result
}

// printCurrentUserCheckingInfoStatus 打印当前用户检测信息状态（调试用）
func (a *App) printCurrentUserCheckingInfoStatus(userName, context string) {
	a.checkingInfoMutex.RLock()
	defer a.checkingInfoMutex.RUnlock()

	fmt.Printf("\n" + strings.Repeat("=", 80) + "\n")
	fmt.Printf("🔍 [全局变量调试] %s - currentUserCheckingInfo 内容\n", context)
	fmt.Printf("用户名: %s\n", userName)
	fmt.Printf("时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Printf(strings.Repeat("-", 80) + "\n")

	userInfo, exists := a.currentUserCheckingInfo[userName]
	if !exists {
		fmt.Printf("❌ 用户检测信息不存在于全局变量中\n")
		fmt.Printf("📊 全局变量currentUserCheckingInfo中的所有用户:\n")
		if len(a.currentUserCheckingInfo) == 0 {
			fmt.Printf("   (空 - 没有任何用户数据)\n")
		} else {
			for user := range a.currentUserCheckingInfo {
				fmt.Printf("   - %s\n", user)
			}
		}
		fmt.Printf(strings.Repeat("=", 80) + "\n\n")
		return
	}
	fmt.Printf("✅ 用户检测信息=======================================》》》\n")
	fmt.Printf("✅ 用户检测信息存在\n")
	fmt.Printf("📈 基础统计:\n")
	fmt.Printf("   总任务数: %d\n", userInfo.TotalTasks)
	fmt.Printf("   已完成任务数: %d\n", userInfo.CompletedTasks)
	fmt.Printf("   截图任务数组长度: %d\n", len(userInfo.ScreenshotTasks))
	fmt.Printf("   用户ID: %s\n", userInfo.UserID)
	fmt.Printf("   站点ID: %s\n", userInfo.SiteID)
	fmt.Printf("   报到号: %s\n", userInfo.RegistrationNo)

	// 🔥 重点显示任务核心数据字段
	fmt.Printf("\n🎯 任务核心数据字段:\n")
	fmt.Printf("   TotalTasks (总任务数): %d\n", userInfo.TotalTasks)
	fmt.Printf("   CompletedTasks (已完成任务数): %d\n", userInfo.CompletedTasks)
	fmt.Printf("   ScreenshotTasks数组长度: %d\n", len(userInfo.ScreenshotTasks))
	_, _, percentage := userInfo.GetProgress()
	fmt.Printf("   进度: %.1f%%\n", percentage)

	fmt.Printf("\n🔄 任务详细数据 (ScreenshotTasks数组内容):\n")
	if len(userInfo.ScreenshotTasks) == 0 {
		fmt.Printf("   ❌ ScreenshotTasks数组为空\n")
	} else {
		// 打印每个任务的详细状态
		for i, task := range userInfo.ScreenshotTasks {
			if task.TaskID == "" {
				fmt.Printf("   ┌─ 任务 T%02d (数组索引[%d]) ──────────────────────────┐\n", i+1, i)
				fmt.Printf("   │ 状态: %-44s │\n", "未分配")
				fmt.Printf("   └─────────────────────────────────────────────────────┘\n")
				continue
			}

			fmt.Printf("   ┌─ 任务 T%02d (TaskID: %d) ──────────────────────────┐\n", i+1, task.TaskID)
			fmt.Printf("   │ 器官名称: %-40s │\n", task.OrganName)
			fmt.Printf("   │ 模式: %-44s │\n", task.Mode)
			fmt.Printf("   │ 状态: %-44s │\n", string(task.Status))

			// 图片信息
			imageStatus := "✗ 未设置"
			if task.InputImage != "" {
				imageStatus = "✓ " + filepath.Base(task.InputImage)
			}
			fmt.Printf("   │ 图片: %-42s │\n", imageStatus)
			fmt.Printf("   │ 识别文本数: %-35d │\n", len(task.RecTexts))

			// 分析数据
			if task.Mode == "B" || task.Mode == "B02" {
				fmt.Printf("   │ 生化分析数: %-35d │\n", len(task.BiochemicalAnalysis))
			} else if task.Mode == "C" || task.Mode == "C03" {
				fmt.Printf("   │ 病理分析数: %-35d │\n", len(task.PathologyAnalysis))
			}

			// 时间信息
			fmt.Printf("   │ 创建时间: %-37s │\n", task.CreatedTime)
			// UpdatedAt field doesn't exist in ScreenshotTask struct

			fmt.Printf("   └─────────────────────────────────────────────────────┘\n")
		}
	}

	// 打印器官统计
	fmt.Printf("\n🏥 检测到的器官统计:\n")
	if len(userInfo.DetectedOrgans) == 0 {
		fmt.Printf("   (暂无器官检测记录)\n")
	} else {
		for organ, count := range userInfo.DetectedOrgans {
			fmt.Printf("   📍 %s: %d次\n", organ, count)
		}
	}

	// 打印JSON格式的完整数据（用于深度调试）
	fmt.Printf("✅ 用户检测信息，完整JSON数据:=======================================》》》\n")

	fmt.Printf("\n📄 完整JSON数据:\n")
	jsonData, err := json.MarshalIndent(userInfo, "   ", "  ")
	if err != nil {
		fmt.Printf("   JSON序列化失败: %v\n", err)
	} else {
		// 限制输出长度，避免终端过于冗长
		if len(jsonData) > 2000 {
			fmt.Printf("   %s...\n   (数据过长，已截断，完整数据长度: %d 字节)\n", string(jsonData[:2000]), len(jsonData))
		} else {
			fmt.Printf("   %s\n", string(jsonData))
		}
	}

	fmt.Printf(strings.Repeat("=", 80) + "\n\n")
}

// createOCRTaskContext 创建OCR任务上下文（线程安全）
func (a *App) createOCRTaskContext(userName, mode, imagePath string) *OCRTaskContext {
	taskID := fmt.Sprintf("%s_%s_%d", userName, mode, time.Now().UnixNano())

	context := &OCRTaskContext{
		TaskID:    taskID,
		UserName:  userName,
		Mode:      mode,
		ImagePath: imagePath,
		StartTime: time.Now(),
		Completed: false,
	}

	a.ocrTaskMutex.Lock()
	a.ocrTaskContexts[taskID] = context
	a.ocrTaskMutex.Unlock()

	fmt.Printf("[OCR-TASK] 创建OCR任务上下文: %s\n", taskID)
	return context
}

// completeOCRTaskContext 完成OCR任务上下文（线程安全）
func (a *App) completeOCRTaskContext(taskID string, result *services.OCRResult, err error) {
	a.ocrTaskMutex.Lock()
	defer a.ocrTaskMutex.Unlock()

	if context, exists := a.ocrTaskContexts[taskID]; exists {
		context.Completed = true
		context.Result = result
		context.Error = err
		fmt.Printf("[OCR-TASK] 完成OCR任务上下文: %s, 成功: %t\n", taskID, err == nil)
	}
}

// notifyTaskManagerCompletion 通知任务管理器任务完成状态
func (a *App) notifyTaskManagerCompletion(ocrTaskID string, success bool, err error) {
	// 这个方法用于确保OCR任务完成后，相关的任务管理器任务也被正确标记为完成
	// 由于OCR任务ID和任务管理器任务ID可能不同，这里主要用于日志记录和状态同步
	if success {
		fmt.Printf("[TASK-SYNC] OCR任务成功完成，任务ID: %s\n", ocrTaskID)
	} else {
		fmt.Printf("[TASK-SYNC] OCR任务失败，任务ID: %s, 错误: %v\n", ocrTaskID, err)
	}

	// 触发任务管理器的清理检查
	if a.taskManager != nil && a.taskManager.IsRunning() {
		// 这里可以添加更多的同步逻辑，确保任务状态一致性
		fmt.Printf("[TASK-SYNC] 任务管理器状态同步检查完成\n")
	}
}

// isOCRTaskDuplicate 检查是否为重复的OCR任务（线程安全）
func (a *App) isOCRTaskDuplicate(userName, mode string, imagePath string) bool {
	a.ocrTaskMutex.RLock()
	defer a.ocrTaskMutex.RUnlock()

	fmt.Printf("[OCR-TASK] 检查重复任务 - 用户: %s, 模式: %s, 图片: %s\n", userName, mode, imagePath)
	fmt.Printf("[OCR-TASK] 当前任务上下文数量: %d\n", len(a.ocrTaskContexts))

	// 只检查完全相同的图片路径，支持并发处理不同的截图
	for taskID, context := range a.ocrTaskContexts {
		// 只有当图片路径完全相同且任务已完成时才认为是重复
		if context.ImagePath == imagePath && context.Completed {
			fmt.Printf("[OCR-TASK] 发现相同图片的已完成任务: %s\n", taskID)
			return true
		}
		// 如果是相同图片路径但任务正在进行中，也认为是重复（避免重复处理同一张图片）
		if context.ImagePath == imagePath && !context.Completed {
			fmt.Printf("[OCR-TASK] 发现相同图片的进行中任务: %s\n", taskID)
			return true
		}
	}

	fmt.Printf("[OCR-TASK] 重复任务检测结果: 无重复任务，支持并发执行\n")
	return false
}

// cleanupOldOCRTasks 清理旧的OCR任务上下文（定期调用）
func (a *App) cleanupOldOCRTasks() {
	a.ocrTaskMutex.Lock()
	defer a.ocrTaskMutex.Unlock()

	cutoffTime := time.Now().Add(-1 * time.Hour) // 清理1小时前的任务
	var toDelete []string

	for taskID, context := range a.ocrTaskContexts {
		if context.StartTime.Before(cutoffTime) {
			toDelete = append(toDelete, taskID)
		}
	}

	for _, taskID := range toDelete {
		delete(a.ocrTaskContexts, taskID)
	}

	if len(toDelete) > 0 {
		fmt.Printf("[OCR-TASK] 清理了 %d 个旧的OCR任务上下文\n", len(toDelete))
	}
}

// TestAllTasksCompletion 测试20个任务完成功能（开发调试用）
func (a *App) TestAllTasksCompletion(userName string) error {
	a.checkingInfoMutex.RLock()
	userInfo, exists := a.currentUserCheckingInfo[userName]
	a.checkingInfoMutex.RUnlock()

	if !exists {
		return fmt.Errorf("用户 %s 的检测信息不存在", userName)
	}

	// 模拟20个任务完成
	userInfo.MarkCompleted()

	// 调用处理函数
	return a.handleAllTasksCompleted(userName, userInfo)
}

// GetUserCheckingInfoStatus 获取用户检测信息状态（用于前端显示）
func (a *App) GetUserCheckingInfoStatus(userName string) (map[string]interface{}, error) {
	a.checkingInfoMutex.RLock()
	defer a.checkingInfoMutex.RUnlock()

	userInfo, exists := a.currentUserCheckingInfo[userName]
	if !exists {
		return map[string]interface{}{
			"exists":  false,
			"message": "用户检测信息不存在",
		}, nil
	}

	return map[string]interface{}{
		"exists":          true,
		"user_name":       userInfo.UserName,
		"total_tasks":     userInfo.TotalTasks,
		"completed_tasks": userInfo.CompletedTasks,
		"progress":        func() float64 { _, _, percentage := userInfo.GetProgress(); return percentage }(),
		"detected_organs": userInfo.DetectedOrgans,
		"checking_time":   userInfo.CheckingTime,
		"completion_time": userInfo.CompletionTime,
		"is_completed":    userInfo.IsCompleted(),
	}, nil
}

// handleAllTasksCompleted 处理20个截图任务完成后的API调用
func (a *App) handleAllTasksCompleted(userName string, userInfo *models.CurrentUserCheckingInfo) error {
	utils.LogInfo(fmt.Sprintf("开始处理用户 %s 的20个截图任务完成后续流程", userName))

	// 发送前端Toast通知
	a.showToastNotification("AI分析进行中", "进行AI大模型检测结果分析，并生成健康评估报告", "info", 5000, true, 0)

	// 1. 调用扣子API
	cozeErr := a.callCozeAPI(userInfo)
	if cozeErr != nil {
		utils.LogError("调用扣子API失败", userName, cozeErr)
		a.showToastNotification("API调用失败", fmt.Sprintf("扣子API调用失败: %v", cozeErr), "error", 5000, false, 0)
	} else {
		utils.LogInfo(fmt.Sprintf("用户 %s 扣子API调用成功", userName))
	}

	// 2. 调用云函数API
	cloudErr := a.callCloudFunctionAPI(userInfo)
	if cloudErr != nil {
		utils.LogError("调用云函数API失败", userName, cloudErr)
		a.showToastNotification("API调用失败", fmt.Sprintf("云函数API调用失败: %v", cloudErr), "error", 5000, false, 0)
	} else {
		utils.LogInfo(fmt.Sprintf("用户 %s 云函数API调用成功", userName))
	}

	// 3. 根据API调用结果发送最终通知
	if cozeErr == nil && cloudErr == nil {
		a.showToastNotification("检测完成", "20个截图任务数据已成功上传，AI分析报告生成中", "success", 8000, false, 100)
		utils.LogInfo(fmt.Sprintf("用户 %s 的20个截图任务流程全部完成", userName))
	} else {
		a.showToastNotification("部分失败", "检测数据上传部分失败，请检查网络连接", "warning", 8000, false, 0)
	}

	return nil
}

// callCozeAPI 调用扣子API
func (a *App) callCozeAPI(userInfo *models.CurrentUserCheckingInfo) error {
	utils.LogInfo("开始调用扣子API")

	// 获取配置
	cozeConfig := a.config.APIKeys.Coze
	if cozeConfig.Token == "" {
		return fmt.Errorf("扣子API配置不完整：缺少token")
	}

	// 构建请求数据 (暂时使用占位数据，后续根据实际API文档完善)
	requestData := map[string]interface{}{
		"user_info":   userInfo,
		"workflow_id": cozeConfig.WorkflowIDCheckUserInfo,
		"space_id":    cozeConfig.SpaceID,
		"app_id":      cozeConfig.AppID,
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("序列化扣子API请求数据失败: %v", err)
	}

	utils.LogInfo(fmt.Sprintf("扣子API请求数据大小: %d 字节", len(jsonData)))
	fmt.Printf("[DEBUG] 扣子API调用 - 用户: %s, 数据大小: %d 字节\n", userInfo.UserName, len(jsonData))

	// TODO: 实际的HTTP请求调用
	// 这里暂时使用占位代码，实际实现需要根据扣子API文档
	fmt.Printf("[PLACEHOLDER] 调用扣子API: workflow_id=%s, user=%s\n", cozeConfig.WorkflowIDCheckUserInfo, userInfo.UserName)

	return nil
}

// callCloudFunctionAPI 调用云函数API
func (a *App) callCloudFunctionAPI(userInfo *models.CurrentUserCheckingInfo) error {
	utils.LogInfo("开始调用云函数API")

	// 获取配置
	cloudConfig := a.config.APIKeys.CloudFunction
	if cloudConfig.UserDetectRawResultDataURL == "" {
		return fmt.Errorf("云函数API配置不完整：缺少URL")
	}

	// 构建请求数据 (暂时使用占位数据，后续根据实际API文档完善)
	requestData := map[string]interface{}{
		"user_checking_info": userInfo,
		"site_id":            a.config.SiteInfo.SiteID,
		"device_mac":         a.config.DeviceInfo.MACAddress,
		"timestamp":          time.Now().Unix(),
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("序列化云函数API请求数据失败: %v", err)
	}

	utils.LogInfo(fmt.Sprintf("云函数API请求数据大小: %d 字节", len(jsonData)))
	fmt.Printf("[DEBUG] 云函数API调用 - URL: %s, 用户: %s, 数据大小: %d 字节\n", cloudConfig.UserDetectRawResultDataURL, userInfo.UserName, len(jsonData))

	// TODO: 实际的HTTP请求调用
	// 这里暂时使用占位代码，实际实现需要根据云函数API文档
	fmt.Printf("[PLACEHOLDER] 调用云函数API: %s, user=%s\n", cloudConfig.UserDetectRawResultDataURL, userInfo.UserName)

	return nil
}

// showToastNotification 显示Toast通知
func (a *App) showToastNotification(title, message, notificationType string, duration int, showProgress bool, progress int) {
	toastData := ToastNotificationData{
		Title:        title,
		Message:      message,
		Type:         notificationType,
		Duration:     duration,
		ShowProgress: showProgress,
		Progress:     progress,
	}

	// 发送到前端
	runtime.EventsEmit(a.ctx, "showToastNotification", toastData)
	utils.LogInfo(fmt.Sprintf("发送Toast通知: %s - %s", title, message))
}

// GetTaskManagerStatus 获取任务管理器状态信息（用于前端显示）
func (a *App) GetTaskManagerStatus() map[string]interface{} {
	if a.taskManager == nil {
		return map[string]interface{}{
			"active_tasks": []interface{}{},
			"stats": map[string]interface{}{
				"total_submitted": 0,
				"active_count":    0,
				"queue_size":      0,
			},
		}
	}

	// 获取活跃任务列表
	activeTasks := a.taskManager.GetActiveTasks()

	// 获取统计信息
	stats := a.taskManager.GetStats()

	return map[string]interface{}{
		"active_tasks": activeTasks,
		"stats": map[string]interface{}{
			"total_submitted": stats.TotalSubmitted,
			"active_count":    stats.ActiveTasks,
			"completed_tasks": stats.TotalCompleted,
			"failed_tasks":    stats.TotalFailed,
			"cancelled_tasks": stats.TotalCancelled,
			"queue_size":      a.taskManager.GetQueueSize(),
		},
	}
}

// GetFailedTasks 获取失败任务列表
func (a *App) GetFailedTasks() map[string]interface{} {
	if a.failedTaskManager == nil {
		return map[string]interface{}{
			"error": "失败任务管理器未初始化",
			"tasks": make(map[string]interface{}),
			"count": map[string]int{"total": 0, "pending": 0, "retrying": 0, "failed": 0, "completed": 0},
		}
	}

	tasks := a.failedTaskManager.GetFailedTasks()
	count := a.failedTaskManager.GetTaskCount()

	return map[string]interface{}{
		"tasks": tasks,
		"count": count,
	}
}

// ClearCompletedFailedTasks 清理已完成的失败任务
func (a *App) ClearCompletedFailedTasks() {
	if a.failedTaskManager != nil {
		a.failedTaskManager.ClearCompletedTasks()
	}
}

// GetCurrentPatientName 获取当前患者姓名（公开方法，实现AppInterface接口）
func (a *App) GetCurrentPatientName() string {
	return a.getCurrentPatientName()
}

// GetCurrentPatient 获取当前患者完整信息
func (a *App) GetCurrentPatient() *models.Registration {
	a.patientMu.RLock()
	defer a.patientMu.RUnlock()

	// 场景4：如果设置了历史患者姓名，需要从列表中查找对应的患者信息
	if a.currentPatientName != "" {
		today := time.Now().Format("2006-01-02")
		registrations, err := a.GetRegistrations(today)
		if err == nil {
			for _, reg := range registrations {
				if reg.Name == a.currentPatientName {
					return &reg
				}
			}
		}
		// 如果找不到历史患者，返回nil
		return nil
	}

	// 场景1-3：从待检测候检者列表获取当前选中的患者
	// 注意：这里必须使用GetPendingRegistrations，因为索引是基于待检测列表设置的
	pendingRegistrations, err := a.GetPendingRegistrations()
	if err != nil || len(pendingRegistrations) == 0 {
		return nil
	}

	// 检查当前选中索引是否有效
	if a.currentPatientIndex >= 0 && a.currentPatientIndex < len(pendingRegistrations) {
		return &pendingRegistrations[a.currentPatientIndex]
	}

	// 默认返回第一个待检测候检者
	if len(pendingRegistrations) > 0 {
		return &pendingRegistrations[0]
	}

	return nil
}

// getCurrentPatientName 获取当前患者姓名
// 支持候检者列表选择和历史患者查看两种场景
func (a *App) getCurrentPatientName() string {
	a.patientMu.RLock()
	defer a.patientMu.RUnlock()

	// 场景4：如果设置了历史患者姓名（用于查看过往检测结果），优先返回
	if a.currentPatientName != "" {
		return a.currentPatientName
	}

	// 场景1-3：从候检者列表获取当前选中的患者
	today := time.Now().Format("2006-01-02")
	registrations, err := a.GetRegistrations(today)
	if err != nil || len(registrations) == 0 {
		// 开发模式下生成测试用户
		if a.config != nil && a.config.Environment == "development" {
			testPatientName := "test-11200"
			fmt.Printf("[开发模式] 候检者列表为空，自动生成测试患者: %s\n", testPatientName)
			return testPatientName
		}
		return "暂无候检者"
	}

	// 检查当前选中索引是否有效
	if a.currentPatientIndex >= 0 && a.currentPatientIndex < len(registrations) {
		if registrations[a.currentPatientIndex].Name != "" {
			return registrations[a.currentPatientIndex].Name
		}
	}

	// 默认返回第一个候检者，如果没有则返回"暂无候检者"
	if len(registrations) > 0 && registrations[0].Name != "" {
		return registrations[0].Name
	}

	// 开发模式下的后备方案
	if a.config != nil && a.config.Environment == "development" {
		testPatientName := "test-11200"
		fmt.Printf("[开发模式] 无有效候检者，使用测试患者: %s\n", testPatientName)
		return testPatientName
	}

	return "暂无候检者"
}

// getCurrentPatientRegistrationNumber 获取当前患者报到号
func (a *App) getCurrentPatientRegistrationNumber() string {
	a.patientMu.RLock()
	defer a.patientMu.RUnlock()

	// 从候检者列表获取当前选中的患者报到号
	today := time.Now().Format("2006-01-02")
	registrations, err := a.GetRegistrations(today)
	if err != nil || len(registrations) == 0 {
		// 开发模式下生成测试报到号
		if a.config != nil && a.config.Environment == "development" {
			testRegistrationNumber := "11200"
			fmt.Printf("[开发模式] 候检者列表为空，自动生成测试报到号: %s\n", testRegistrationNumber)
			return testRegistrationNumber
		}
		return "00000"
	}

	// 检查当前选中索引是否有效
	if a.currentPatientIndex >= 0 && a.currentPatientIndex < len(registrations) {
		if registrations[a.currentPatientIndex].RegistrationNumber != "" {
			return registrations[a.currentPatientIndex].RegistrationNumber
		}
	}

	// 默认返回第一个候检者的报到号
	if len(registrations) > 0 && registrations[0].RegistrationNumber != "" {
		return registrations[0].RegistrationNumber
	}

	// 开发模式下的后备方案
	if a.config != nil && a.config.Environment == "development" {
		testRegistrationNumber := "11200"
		fmt.Printf("[开发模式] 无有效候检者，使用测试报到号: %s\n", testRegistrationNumber)
		return testRegistrationNumber
	}

	return "暂无候检者"
}

// SetCurrentPatientIndex 设置当前选中的候检者索引
// 用于场景1-3：候检者列表选择、自动切换下一位、检测完成后切换
func (a *App) SetCurrentPatientIndex(index int) error {
	a.patientMu.Lock()
	defer a.patientMu.Unlock()

	// 验证索引有效性
	registrations, err := a.GetPendingRegistrations()
	if err != nil {
		return fmt.Errorf("获取待检测候检者列表失败: %v", err)
	}

	if index < 0 || index >= len(registrations) {
		return fmt.Errorf("无效的候检者索引: %d，有效范围: 0-%d", index, len(registrations)-1)
	}

	a.currentPatientIndex = index
	// 清空历史患者姓名，确保使用候检者列表
	a.currentPatientName = ""

	utils.LogOperation("设置当前候检者", registrations[index].Name, a.config.SiteInfo.SiteID)
	return nil
}

// SetCurrentPatientName 设置当前患者姓名
// 用于场景4：查看历史患者的检测结果
func (a *App) SetCurrentPatientName(patientName string) {
	a.patientMu.Lock()
	defer a.patientMu.Unlock()

	a.currentPatientName = patientName
	utils.LogOperation("设置当前患者（历史查看）", patientName, a.config.SiteInfo.SiteID)
}

// GetCurrentPatientIndex 获取当前选中的候检者索引
func (a *App) GetCurrentPatientIndex() int {
	a.patientMu.RLock()
	defer a.patientMu.RUnlock()
	return a.currentPatientIndex
}

// MoveToNextPatient 自动切换到下一位候检者
// 用于场景3：前一个受检者检测完成后自动切换
func (a *App) MoveToNextPatient() error {
	a.patientMu.Lock()
	defer a.patientMu.Unlock()

	// 获取待检测患者列表（过滤掉已完成的）
	pendingRegistrations, err := a.GetPendingRegistrations()
	if err != nil {
		return fmt.Errorf("获取待检测候检者列表失败: %v", err)
	}

	if len(pendingRegistrations) == 0 {
		// 没有待检测患者了
		a.currentPatientIndex = -1
		utils.LogOperation("移动到下一位患者", "无待检测患者", a.config.SiteInfo.SiteID)
		return nil
	}

	// 移动到下一位患者
	nextIndex := a.currentPatientIndex + 1
	if nextIndex >= len(pendingRegistrations) {
		// 如果已经是最后一位，回到第一位
		nextIndex = 0
	}

	a.currentPatientIndex = nextIndex
	// 清空历史患者姓名，确保使用候检者列表
	a.currentPatientName = ""

	utils.LogOperation("移动到下一位患者", pendingRegistrations[nextIndex].Name, a.config.SiteInfo.SiteID)
	return nil
}

// ClearCurrentPatient 清空当前患者状态
func (a *App) ClearCurrentPatient() {
	a.patientMu.Lock()
	defer a.patientMu.Unlock()

	a.currentPatientIndex = 0
	a.currentPatientName = ""
	utils.LogOperation("清空当前患者状态", "", a.config.SiteInfo.SiteID)
}

// UploadLatestScreenshot 上传最新的截图文件
// 注释原因：A、B、C三个截图快捷键都通过ProcessScreenshotWorkflow函数处理，此函数已不再需要
// func (a *App) UploadLatestScreenshot() (string, error) {
// 	utils.LogOperation("上传最新截图", "医生或健康专家", a.config.SiteInfo.SiteID)

// 	// 获取pic目录下最新的PNG文件
// 	files, err := filepath.Glob("pic/*.png")
// 	if err != nil || len(files) == 0 {
// 		return "", fmt.Errorf("未找到截图文件")
// 	}

// 	// 按修改时间排序，获取最新的文件
// 	latestFile := files[0]
// 	latestTime, err := os.Stat(latestFile)
// 	if err != nil {
// 		return "", fmt.Errorf("获取文件信息失败: %v", err)
// 	}

// 	for _, file := range files[1:] {
// 		fileInfo, err := os.Stat(file)
// 		if err != nil {
// 			continue
// 		}
// 		if fileInfo.ModTime().After(latestTime.ModTime()) {
// 			latestFile = file
// 			latestTime = fileInfo
// 		}
// 	}

// 	// 上传图片到DCloud
// 	picURL, err := a.apiService.UploadImageToDCloud(latestFile)
// 	if err != nil {
// 		utils.LogError("上传图片失败", "医生或健康专家", err)
// 		return "", fmt.Errorf("上传图片失败: %v", err)
// 	}

// 	// 调用扣子工作流
// 	reportID := fmt.Sprintf("医生或健康专家-%s", time.Now().Format("20060102_150405"))
// 	if err := a.apiService.CallCozeWorkflow(picURL, latestFile, reportID); err != nil {
// 		utils.LogError("调用扣子工作流失败", "医生或健康专家", err)
// 		// 注意：这里不返回错误，因为图片已经成功上传
// 		utils.LogWarning(fmt.Sprintf("扣子工作流调用失败，但图片已上传: %s", picURL))
// 	}

// 	return picURL, nil
// }

// GenerateQRCode 生成患者二维码 (旧版，可能需要调整或移除)
func (a *App) GenerateQRCode() (string, error) {
	utils.LogOperation("生成患者二维码", "", a.config.SiteInfo.SiteID)

	// 使用带缓存的二维码生成方法
	_, filePath, err := a.qrcodeService.GenerateCustomAppQRCodeWithCache() // 这实际上是生成报到二维码的逻辑
	if err != nil {
		utils.LogError("生成患者二维码失败", "", err)
		return "", err
	}

	// 记录是否使用了缓存
	if a.configService.IsSiteInfoChanged() {
		utils.LogInfo("重新生成了患者二维码")
	} else {
		utils.LogInfo("复用了缓存的患者二维码")
	}

	return filePath, nil
}

// GenerateRegistrationQRCode 生成报到二维码并返回Base64编码的图片和文件路径
func (a *App) GenerateRegistrationQRCode() (map[string]string, error) {
	utils.LogOperation("生成报到二维码", "", a.config.SiteInfo.SiteID)

	// 使用带缓存的二维码生成方法
	qrCodeBytes, filePath, err := a.qrcodeService.GenerateCustomAppQRCodeWithCache()
	if err != nil {
		utils.LogError("生成报到二维码失败", "", err)
		return nil, fmt.Errorf("生成报到二维码失败: %v", err)
	}

	// 将二维码图片字节转换为Base64字符串
	qrCodeBase64 := utils.EncodeBytesToBase64(qrCodeBytes)

	// 记录是否使用了缓存
	if a.configService.IsSiteInfoChanged() {
		utils.LogInfo("重新生成了报到二维码")
	} else {
		utils.LogInfo("复用了缓存的报到二维码")
	}

	return map[string]string{
		"qr_code_base64": qrCodeBase64,
		"file_path":      filePath,
	}, nil
}

// AddPatient 添加患者
func (a *App) AddPatient(name string) error {
	utils.LogOperation("添加患者", name, a.config.SiteInfo.SiteID)

	err := a.patientService.AddPatient(name)
	if err != nil {
		utils.LogError("添加患者", name, err)
	}

	return err
}

// GetPatientList 获取患者列表
func (a *App) GetPatientList() []models.Patient {
	return a.patientService.GetPatientList()
}

// GetSiteInfo 获取站点信息
func (a *App) GetSiteInfo() (*models.SiteInfo, error) {
	utils.LogInfo("GetSiteInfo called - 开始获取站点信息")

	// 优先使用缓存的站点信息，避免重复API调用
	// 如果缓存为空或需要强制更新，才调用API
	if a.config.SiteInfo.SiteID == "" || a.config.SiteInfo.SiteName == "" {
		utils.LogInfo("站点信息缓存为空，从API加载")
		err := a.configService.LoadSiteInfoFromAPIWithCache(false)
		if err != nil {
			utils.LogError("GetSiteInfo - 从API加载站点信息失败", "", err)
			return nil, fmt.Errorf("获取站点信息失败: %v", err)
		}
		// 更新当前配置
		a.config = a.configService.GetConfig()
	} else {
		utils.LogInfo("使用缓存的站点信息")
	}

	// 返回站点信息
	siteInfo := &a.config.SiteInfo
	utils.LogInfo(fmt.Sprintf("GetSiteInfo - 成功获取站点信息: ID=%s, Name=%s", siteInfo.SiteID, siteInfo.SiteName))
	return siteInfo, nil
}

// RemovePatient 移除患者
func (a *App) RemovePatient(index int) error {
	utils.LogOperation("移除患者", "", a.config.SiteInfo.SiteID)

	err := a.patientService.RemovePatient(index)
	if err != nil {
		utils.LogError("移除患者", "", err)
	}

	return err
}

// ClearPatientList 清空患者列表
func (a *App) ClearPatientList() {
	utils.LogOperation("清空患者列表", "", a.config.SiteInfo.SiteID)
	a.patientService.ClearPatientList()
}

// GetCurrentRegistrationNumber 获取当前挂号序号
func (a *App) GetCurrentRegistrationNumber() int {
	return a.patientService.GetCurrentRegistrationNumber()
}

// GetRegistrations 获取候检者列表（包含所有状态的患者）
func (a *App) GetRegistrations(date string) ([]models.Registration, error) {
	utils.LogOperation("获取候检者列表", "", a.config.SiteInfo.SiteID)

	registrations, err := a.apiService.GetRegistrations(date)
	if err != nil {
		utils.LogError("获取候检者列表", "", err)
		return nil, err
	}

	return registrations, nil
}

// GetPendingRegistrations 获取待检测的候检者列表（过滤掉已完成的）
func (a *App) GetPendingRegistrations() ([]models.Registration, error) {
	utils.LogOperation("获取待检测候检者列表", "", a.config.SiteInfo.SiteID)

	today := time.Now().Format("2006-01-02")
	allRegistrations, err := a.apiService.GetRegistrations(today)
	if err != nil {
		utils.LogError("获取待检测候检者列表", "", err)
		return nil, err
	}

	// 过滤掉已完成的患者
	pendingRegistrations := make([]models.Registration, 0)
	for _, reg := range allRegistrations {
		if !reg.IsCompleted {
			pendingRegistrations = append(pendingRegistrations, reg)
		}
	}

	return pendingRegistrations, nil
}

// 用api_service.go 下的GetRegistrations 的 云函数getRegistrationsBySiteAndDevice替代
// GetCompletedPatients 获取今日已完成检测的患者列表
func (a *App) GetCompletedPatients() ([]models.Registration, error) {
	utils.LogOperation("获取已完成患者列表", "", a.config.SiteInfo.SiteID)

	completedPatients, err := a.apiService.GetCompletedPatients()
	if err != nil {
		utils.LogError("获取已完成患者列表", "", err)
		return nil, err
	}

	return completedPatients, nil
}

// GetCompletedPatientsByDate 获取指定日期已完成检测的患者列表
func (a *App) GetCompletedPatientsByDate(date string) ([]models.Registration, error) {
	utils.LogOperation("获取指定日期已完成患者列表", date, a.config.SiteInfo.SiteID)

	completedPatients, err := a.apiService.GetCompletedPatientsByDate(date)
	if err != nil {
		utils.LogError("获取指定日期已完成患者列表", date, err)
		return nil, err
	}

	return completedPatients, nil
}

// GetPendingPatients 获取指定日期的候检者列表（已报到未检测）
func (a *App) GetPendingPatients(date string) ([]models.Registration, error) {
	utils.LogOperation("获取候检者列表", date, a.config.SiteInfo.SiteID)

	pendingPatients, err := a.apiService.GetPendingPatients(date)
	if err != nil {
		utils.LogError("获取候检者列表", date, err)
		return nil, err
	}

	return pendingPatients, nil
}

// GetUnanalyzedPatients 获取指定日期已完成检测但未分析结果的患者列表
func (a *App) GetUnanalyzedPatients(date string) ([]models.Registration, error) {
	utils.LogOperation("获取未分析患者列表", date, a.config.SiteInfo.SiteID)

	unanalyzedPatients, err := a.apiService.GetUnanalyzedPatients(date)
	if err != nil {
		utils.LogError("获取未分析患者列表", date, err)
		return nil, err
	}

	return unanalyzedPatients, nil
}

// MarkPatientCompleted 标记患者完成10轮检测
func (a *App) MarkPatientCompleted(userID string, registrationNumber string) error {
	utils.LogOperation("标记患者完成检测", fmt.Sprintf("用户ID:%s, 报到号:%s", userID, registrationNumber), a.config.SiteInfo.SiteID)

	err := a.apiService.MarkPatientCompleted(userID, registrationNumber)
	if err != nil {
		utils.LogError("标记患者完成检测", fmt.Sprintf("用户ID:%s, 报到号:%s", userID, registrationNumber), err)
		return err
	}

	return nil
}

// GetTodayPatientCount 获取今日患者数量
func (a *App) GetTodayPatientCount() int {
	return a.patientService.GetTodayPatientCount()
}

// GetModeConfig 获取模式配置
func (a *App) GetModeConfig() map[string]models.ModeInfo {
	return a.configService.GetModeConfig()
}

// ToggleWindowSize 切换窗体大小
func (a *App) ToggleWindowSize() {
	if a.isExpanded {
		// 收缩为小窗体
		runtime.WindowSetSize(a.ctx, 380, 800)
		a.setWindowToSide()
	} else {
		// 扩展为大窗体
		runtime.WindowSetSize(a.ctx, 1200, 800)
		runtime.WindowCenter(a.ctx)
	}
	a.isExpanded = !a.isExpanded
	utils.LogOperation("切换窗体大小", "", fmt.Sprintf("扩展状态: %v", a.isExpanded))
}

// SetWindowPosition 设置窗体位置偏好
func (a *App) SetWindowPosition(position string) {
	a.windowPosition = position
	if !a.isExpanded {
		a.setWindowToSide()
	}
	utils.LogOperation("设置窗体位置", "", position)
}

// setWindowToSide 将窗体设置到屏幕边侧
func (a *App) setWindowToSide() {
	// 获取屏幕尺寸
	screens, err := runtime.ScreenGetAll(a.ctx)
	if err != nil || len(screens) == 0 {
		return
	}

	primaryScreen := screens[0]
	screenWidth := primaryScreen.Size.Width
	screenHeight := primaryScreen.Size.Height

	// 使用配置文件中的normal_windows_setting坐标
	if a.config != nil {
		// 根据normal_windows_setting百分比计算实际像素坐标
		x := int(float64(screenWidth) * a.config.NormalWindowsSetting.LeftPercent)
		y := int(float64(screenHeight) * a.config.NormalWindowsSetting.TopPercent)

		// 计算窗口尺寸
		windowWidth := int(float64(screenWidth) * (a.config.NormalWindowsSetting.RightPercent - a.config.NormalWindowsSetting.LeftPercent))
		windowHeight := int(float64(screenHeight) * (a.config.NormalWindowsSetting.BottomPercent - a.config.NormalWindowsSetting.TopPercent))

		// 设置窗口位置和尺寸
		runtime.WindowSetPosition(a.ctx, x, y)
		runtime.WindowSetSize(a.ctx, windowWidth, windowHeight)

		utils.LogInfo(fmt.Sprintf("窗口位置设置为紧凑模式: x=%d, y=%d, width=%d, height=%d", x, y, windowWidth, windowHeight))
	} else {
		// 如果配置为空，使用默认位置
		var x, y int
		if a.windowPosition == "right" {
			// 右侧位置
			x = screenWidth - 380 - 10 // 窗体宽度 + 边距
		} else {
			// 左侧位置（默认）
			x = 10
		}
		y = (screenHeight - 800) / 2 // 垂直居中

		runtime.WindowSetPosition(a.ctx, x, y)
	}
}

// GetWindowState 获取当前窗体状态
func (a *App) GetWindowState() map[string]interface{} {
	return map[string]interface{}{
		"isExpanded": a.isExpanded,
		"position":   a.windowPosition,
	}
}

// MinimizeWindow 最小化窗体
func (a *App) MinimizeWindow() {
	runtime.WindowMinimise(a.ctx)
}

// SetAlwaysOnTop 设置窗体置顶状态
func (a *App) SetAlwaysOnTop(onTop bool) {
	if onTop {
		runtime.WindowSetAlwaysOnTop(a.ctx, true)
	} else {
		runtime.WindowSetAlwaysOnTop(a.ctx, false)
	}
	utils.LogOperation(fmt.Sprintf("设置窗体置顶(置顶: %v)", onTop), "", a.config.SiteInfo.SiteID)
}

// HandleKeyboardShortcut 处理键盘快捷键
func (a *App) HandleKeyboardShortcut(key string) {
	switch key {
	case "F11":
		a.ToggleWindowSize()
	case "F10":
		// 切换位置
		if a.windowPosition == "left" {
			a.SetWindowPosition("right")
		} else {
			a.SetWindowPosition("left")
		}
	case "F9":
		a.MinimizeWindow()
	case "F1":
		// 器官问题来源分析截图 - 使用任务管理器
		a.SubmitScreenshotTask(services.TaskTypeScreenshotA, "A", "器官问题来源分析")
	case "F2":
		// 生化平衡分析截图 - 使用任务管理器
		a.SubmitScreenshotTask(services.TaskTypeScreenshotB, "B", "生化平衡分析")
	case "F3":
		// 病理形态学分析截图 - 使用任务管理器
		a.SubmitScreenshotTask(services.TaskTypeScreenshotC, "C", "病理形态学分析")
	case "Ctrl+Shift+A":
		// 器官问题来源分析截图
		go func() {
			_, err := a.ProcessScreenshotWorkflow("器官问题来源分析")
			if err != nil {
				utils.LogError("快捷键截图失败", a.getCurrentPatientName(), err)
			}
		}()
	case "Ctrl+Shift+B":
		// 生化平衡分析截图 - 使用任务管理器
		a.SubmitScreenshotTask(services.TaskTypeScreenshotB, "B", "生化平衡分析")
	case "Ctrl+Shift+C":
		// 病理形态学分析截图 - 使用任务管理器
		a.SubmitScreenshotTask(services.TaskTypeScreenshotC, "C", "病理形态学分析")
		// case "Ctrl+Shift+U":
		// 	// 上传最新截图 - 已注释，功能已由A、B、C快捷键的ProcessScreenshotWorkflow替代
		// 	go func() {
		// 		_, err := a.UploadLatestScreenshot()
		// 		if err != nil {
		// 			utils.LogError("快捷键上传失败", "医生或健康专家", err)
		// 		}
		// 	}()
	}
}

// AutoCollapseAfterInactivity 长时间无操作自动收缩
func (a *App) AutoCollapseAfterInactivity() {
	if a.isExpanded {
		a.ToggleWindowSize()
		utils.LogOperation("自动收缩窗体", "", "长时间无操作")
	}
}

// HandleHotkey 处理快捷键事件
func (a *App) HandleHotkey(hotkey string) {
	switch hotkey {
	case "Ctrl+X":
		// 退出程序
		runtime.Quit(a.ctx)
	case "F1":
		// 器官问题来源分析截图
		go func() {
			_, err := a.ProcessScreenshotWorkflow("A")
			if err != nil {
				utils.LogError("器官问题来源分析截图失败", a.getCurrentPatientName(), err)
			}
		}()
	case "F2":
		// 生化平衡分析截图
		go func() {
			_, err := a.ProcessScreenshotWorkflow("B")
			if err != nil {
				utils.LogError("生化平衡分析截图失败", a.getCurrentPatientName(), err)
			}
		}()
	case "F3":
		// 病理形态学分析截图
		go func() {
			_, err := a.ProcessScreenshotWorkflow("C")
			if err != nil {
				utils.LogError("病理形态学分析截图失败", a.getCurrentPatientName(), err)
			}
		}()
	case "Ctrl+Shift+A":
		// 器官问题来源分析截图
		go func() {
			_, err := a.ProcessScreenshotWorkflow("器官问题来源分析")
			if err != nil {
				utils.LogError("快捷键截图失败", a.getCurrentPatientName(), err)
			}
		}()
	case "Ctrl+Shift+B":
		// 生化平衡分析截图
		go func() {
			_, err := a.ProcessScreenshotWorkflow("生化平衡分析")
			if err != nil {
				utils.LogError("快捷键截图失败", a.getCurrentPatientName(), err)
			}
		}()
	case "Ctrl+Shift+C":
		// 病理形态学分析截图
		go func() {
			_, err := a.ProcessScreenshotWorkflow("病理形态学分析")
			if err != nil {
				utils.LogError("快捷键截图失败", a.getCurrentPatientName(), err)
			}
		}()
		// case "Ctrl+Shift+U":
		// 	// 上传最新截图 - 已注释，功能已由A、B、C快捷键的ProcessScreenshotAndUpload替代
		// 	go func() {
		// 		_, err := a.UploadLatestScreenshot()
		// 		if err != nil {
		// 			utils.LogError("快捷键上传失败", "医生或健康专家", err)
		// 		}
		// 	}()
	}
}

// shutdown 应用关闭时的清理工作
func (a *App) shutdown(ctx context.Context) bool {
	utils.LogInfo("应用开始关闭，执行清理工作...")

	// 检查OCR处理器是否有待处理任务
	if a.simpleScreenshotManager != nil && a.simpleScreenshotManager.GetProcessor() != nil {
		if a.simpleScreenshotManager.GetProcessor().HasPendingTasks() {
			taskInfo := a.simpleScreenshotManager.GetProcessor().GetRemainingTasksInfo()

			// 显示任务状态信息
			fmt.Printf("\n⚠️  检测到后台OCR任务正在运行\n")
			fmt.Printf("📋 任务状态: %+v\n", taskInfo)

			if estimatedTime, ok := taskInfo["estimated_time_str"].(string); ok {
				fmt.Printf("⏱️  预计剩余时间: %s\n", estimatedTime)
			}

			fmt.Printf("\n选择操作:\n")
			fmt.Printf("1. 等待任务完成后关闭 (推荐)\n")
			fmt.Printf("2. 立即强制关闭 (可能丢失数据)\n")
			fmt.Printf("\n💡 提示: 下次启动应用时会自动恢复未完成的任务\n")

			// 尝试优雅停止，等待30秒
			fmt.Printf("\n正在等待任务完成，最多等待30秒...\n")
			err := a.simpleScreenshotManager.GetProcessor().GracefulStop(30 * time.Second)
			if err != nil {
				fmt.Printf("⚠️  等待超时，将强制关闭: %v\n", err)
			} else {
				fmt.Printf("✅ 所有OCR任务已完成\n")
			}
		} else {
			fmt.Printf("✅ 没有待处理的OCR任务\n")
		}
	}

	// 停止快捷键服务
	if a.hotkeyService != nil {
		a.hotkeyService.StopHotkeyListener()
		utils.LogInfo("快捷键服务已停止")
	}

	// 停止失败任务管理器
	if a.failedTaskManager != nil {
		a.failedTaskManager.Stop()
		utils.LogInfo("失败任务管理器已关闭")
	}

	// 注释：已移除integratedScreenshotService，使用简化的截图管理
	// 简化的截图管理器会在应用关闭时自动清理
	if a.simpleScreenshotManager != nil {
		a.simpleScreenshotManager.Stop()
		utils.LogInfo("简化截图管理器已关闭")
	}

	// 关闭OCR服务
	if a.ocrService != nil {
		a.ocrService.Close()
		utils.LogInfo("OCR服务已关闭")
	}

	utils.LogInfo("应用清理工作完成")

	// 确保进程完全退出
	go func() {
		time.Sleep(100 * time.Millisecond) // 给日志一点时间写入
		os.Exit(0)
	}()

	return false // 返回false表示允许关闭
}

// TestOCR 测试OCR功能
func (a *App) TestOCR(imagePath string) (map[string]interface{}, error) {
	utils.LogOperation("测试OCR功能", "", a.config.SiteInfo.SiteID)

	if a.ocrService == nil {
		return nil, fmt.Errorf("OCR服务未初始化")
	}

	// 验证OCR环境
	if err := a.ocrService.ValidateOCREnvironment(); err != nil {
		utils.LogError("OCR环境验证失败", "", err)
		return nil, fmt.Errorf("OCR环境验证失败: %v", err)
	}

	// 处理图片并获取详细结果
	result, err := a.ocrService.ProcessImageWithDetails(context.Background(), imagePath)
	if err != nil {
		utils.LogError("OCR处理失败", "", err)
		return nil, fmt.Errorf("OCR处理失败: %v", err)
	}

	utils.LogInfo(fmt.Sprintf("OCR处理成功 - 器官: %s, 置信度: %.2f", result.OrganName, result.Confidence))

	// 将键值对转换为字符串格式
	var keyValueText strings.Builder
	for key, value := range result.KeyValuePairs {
		keyValueText.WriteString(fmt.Sprintf("%s: %s\n", key, value))
	}

	return map[string]interface{}{
		"full_text":       keyValueText.String(),
		"organ_name":      result.OrganName,
		"confidence":      result.Confidence,
		"image_path":      result.ImagePath,
		"key_value_pairs": result.KeyValuePairs,
	}, nil
}

// ExtractOrganFromScreenshot 从截图中提取器官名称
func (a *App) ExtractOrganFromScreenshot(imagePath string) (string, error) {
	utils.LogOperation("提取器官名称", "", a.config.SiteInfo.SiteID)

	if a.ocrService == nil {
		return "", fmt.Errorf("OCR服务未初始化")
	}

	// 使用ProcessImageWithDetails获取完整OCR结果
	ocrResult, err := a.ocrService.ProcessImageWithDetails(context.Background(), imagePath)
	if err != nil {
		utils.LogError("OCR处理失败", "", err)
		return "", err
	}

	// 返回识别的器官名称
	organName := ocrResult.OrganName
	if organName == "" || organName == "未识别" {
		organName = "未知器官"
	}

	utils.LogInfo(fmt.Sprintf("成功提取器官名称: %s", organName))
	return organName, nil
}

// ValidateOCRSetup 验证OCR环境设置
func (a *App) ValidateOCRSetup() error {
	utils.LogOperation("验证OCR环境", "", a.config.SiteInfo.SiteID)

	if a.ocrService == nil {
		return fmt.Errorf("OCR服务未初始化")
	}

	err := a.ocrService.ValidateOCREnvironment()
	if err != nil {
		utils.LogError("OCR环境验证失败", "", err)
	} else {
		utils.LogInfo("OCR环境验证成功")
	}

	return err
}

// TakeOCRScreenshot 专门用于OCR的截图方法
func (a *App) TakeOCRScreenshot(mode string, userName string) (map[string]interface{}, error) {
	utils.LogOperation("OCR专用截图", userName, a.config.SiteInfo.SiteID)

	// 使用OCR专用截图方法
	ocrFilePath, organName, err := a.screenshotService.TakeOCRRegionScreenshot(mode, userName)
	if err != nil {
		utils.LogError("OCR截图失败", userName, err)
		return nil, fmt.Errorf("OCR截图失败: %v", err)
	}

	utils.LogInfo(fmt.Sprintf("OCR截图成功 - 器官: %s", organName))

	return map[string]interface{}{
		"ocr_file_path": ocrFilePath,
		"organ_name":    organName,
		"mode":          mode,
		"user_name":     userName,
		"timestamp":     time.Now().Format("2006-01-02 15:04:05"),
	}, nil
}

// ProcessScreenshotWithOCR 截图并同步进行OCR识别
func (a *App) ProcessScreenshotWithOCR(mode string, userName string) (map[string]interface{}, error) {
	utils.LogOperation("截图+OCR处理", userName, a.config.SiteInfo.SiteID)

	// 1. 执行原有的截图和上传流程
	picURL, err := a.ProcessScreenshotWorkflow(mode)
	if err != nil {
		return nil, fmt.Errorf("截图上传失败: %v", err)
	}

	// 2. 同步执行OCR识别
	ocrFilePath, organName, ocrErr := a.screenshotService.TakeOCRRegionScreenshot(mode, userName)
	if ocrErr != nil {
		// OCR失败不影响主流程，但记录错误
		utils.LogError("OCR识别失败", userName, ocrErr)

		// 将失败的OCR任务添加到失败任务管理器
		if a.failedTaskManager != nil {
			a.failedTaskManager.AddFailedTask(userName, mode, ocrFilePath, ocrErr.Error())
			fmt.Printf("[INFO] 已将失败的OCR任务添加到重试队列: %s\n", ocrFilePath)
		}

		organName = "未知器官"
		ocrFilePath = ""
	}

	return map[string]interface{}{
		"pic_url":       picURL,
		"ocr_file_path": ocrFilePath,
		"organ_name":    organName,
		"mode":          mode,
		"user_name":     userName,
		"timestamp":     time.Now().Format("2006-01-02 15:04:05"),
		"ocr_success":   ocrErr == nil,
	}, nil
}

// updateScreenshotRecord 更新截图记录到云数据库
func (a *App) updateScreenshotRecord(mode, userName, organName, filename, cloudURL string, currentUser *models.Registration) error {

	// 构建参数
	params := models.ScreenshotRecordParams{
		UserName:       userName,
		UserID:         "unknown_user",
		RegistrationID: "unknown_registration",
		AnalysisType:   a.getAnalysisTypeFromMode(mode),
		DetectedOrgan:  organName,
		Filename:       filename,
		CloudURL:       cloudURL,
		OperatorName:   "系统操作员",
		OCRSuccess:     organName != "未知器官",
	}

	// 如果有当前用户信息，使用真实数据
	if currentUser != nil {
		params.UserID = currentUser.UserID
		params.RegistrationID = currentUser.RegistrationNumber
	}

	// 调用API服务更新记录
	return a.apiService.CreateOrUpdateScreenshotRecord(params)
}

// getCurrentRound 已删除 - 使用简化的截图管理
// 原有的轮次管理逻辑已被简化的20个并发截图任务替代

// markModeCompleted 已删除 - 使用简化的截图管理
// 原有的轮次管理逻辑已被简化的20个并发截图任务替代

// getRoundStatus 已删除 - 使用简化的截图管理
// 原有的轮次管理逻辑已被简化的20个并发截图任务替代

// getCurrentRoundNumber 已删除 - 使用简化的截图管理
// syncRoundNumberToSubSystems 已删除 - 使用简化的截图管理
// GetCurrentRoundNumber 已删除 - 使用简化的截图管理

// getAnalysisTypeFromMode 从模式名称获取分析类型代码
func (a *App) getAnalysisTypeFromMode(mode string) string {
	switch mode {
	case "器官问题来源分析":
		return "A01"
	case "生化平衡分析":
		return "B02"
	case "病理形态学分析":
		return "C03"
	default:
		return "B02" // 默认值
	}
}

// showProcessingNotification 显示处理中的通知窗口（步骤3）
func (a *App) showProcessingNotification(organName string) {
	// 在控制台显示处理状态
	fmt.Printf("[INFO] 正在显示处理中的通知窗...\n")

	// 使用主窗体通知显示处理状态
	a.ShowToastNotification("处理中", fmt.Sprintf("正在处理 %s 的检测结果分析数据，请稍候...", organName), "info", 3000, true, 0)

	// 模拟10步处理过程
	go func() {
		for step := 1; step <= 10; step++ {
			time.Sleep(300 * time.Millisecond) // 每步300ms
			fmt.Printf("[INFO] 处理进度: %d/10\n", step)

			// 每3步显示一次进度通知
			if step%3 == 0 || step == 10 {
				if step == 10 {
					a.ShowToastNotification("完成", fmt.Sprintf("%s 分析完成！", organName), "success", 3000, false, 100)
				} else {
					progress := (step * 100) / 10
					a.ShowToastNotification("进度更新", fmt.Sprintf("%s 分析进度: %d/10 (%d%%)", organName, step, progress), "info", 2000, true, progress)
				}
			}
		}

		fmt.Printf("[INFO] 处理完成！\n")
	}()
}

// HandleCozeLLM_AnylizeD_value 处理20个截图任务完成后的扣子LLM分析
// 该方法将收集所有20个截图任务的OCR解析JSON数据，并一次性调用扣子API
func (a *App) HandleCozeLLM_AnylizeD_value(userName string) error {
	fmt.Printf("[DEBUG] 开始处理20个截图任务的扣子LLM分析 - 用户: %s\n", userName)

	// TODO: 实现以下功能
	// 1. 检查用户是否已完成20个截图任务
	// 2. 收集所有20个截图的OCR解析JSON数据
	// 3. 将JSON数据集合并为一个完整的数据结构
	// 4. 调用扣子API进行LLM分析
	// 5. 处理分析结果并保存

	// 获取用户检测信息
	a.checkingInfoMutex.RLock()
	userInfo, exists := a.currentUserCheckingInfo[userName]
	a.checkingInfoMutex.RUnlock()

	if !exists {
		return fmt.Errorf("用户 %s 的检测信息不存在", userName)
	}

	// 检查是否所有任务都已完成
	_, _, percentage := userInfo.GetProgress()
	if percentage < 100.0 {
		return fmt.Errorf("用户 %s 的20个截图任务尚未全部完成，当前进度: %.1f%%", userName, percentage)
	}

	fmt.Printf("[DEBUG] 用户 %s 的20个截图任务已全部完成，准备调用扣子LLM分析\n", userName)

	// 这里暂时返回nil，等待后续实现具体逻辑
	return fmt.Errorf("HandleCozeLLM_AnylizeD_value方法待实现")
}

// CaptureScreenshotByBtn 通过按钮触发的智能截图功能
// 先进行截图和OCR识别，然后根据OCR返回的数字字段自动判断B02或C03模式
func (a *App) CaptureScreenshotByBtn() (map[string]interface{}, error) {
	utils.LogOperation("按钮触发智能截图", "", a.config.SiteInfo.SiteID)

	// 获取当前用户名
	userName := a.getCurrentPatientName()
	if userName == "" {
		return nil, fmt.Errorf("未设置当前受检者信息")
	}

	// 1. 立即创建任务ID和基本信息
	taskID := fmt.Sprintf("btn_%s_%d", userName, time.Now().UnixNano())
	fmt.Printf("[智能截图] 创建异步截图任务 - TaskID: %s, 用户: %s\n", taskID, userName)

	// 2. 启动异步处理
	go func() {
		a.processBtnScreenshotAsync(taskID, userName)
	}()

	// 3. 立即返回，不阻塞UI
	return map[string]interface{}{
		"success":   true,
		"task_id":   taskID,
		"user_name": userName,
		"status":    "processing",
		"message":   "智能截图任务已启动，正在后台处理...",
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	}, nil
}

// processBtnScreenshotAsync 异步处理按钮截图任务
func (a *App) processBtnScreenshotAsync(taskID, userName string) {
	fmt.Printf("[异步处理] 开始处理智能截图任务 - TaskID: %s\n", taskID)

	// 发送任务开始事件
	runtime.EventsEmit(a.ctx, "task-started", map[string]interface{}{
		"task_id":   taskID,
		"task_type": "button_screenshot",
		"user_name": userName,
		"status":    "processing",
	})

	// 1. 计算目标截图区域，直接使用配置文件中的crop_settings
	fullBounds := screenshot.GetDisplayBounds(0)
	var targetBounds image.Rectangle

	if a.config != nil {
		// 根据配置计算实际的截图区域坐标
		width := float64(fullBounds.Dx())
		height := float64(fullBounds.Dy())

		left := int(width * a.config.CropSettings.LeftPercent)
		right := int(width * (1.0 - a.config.CropSettings.RightPercent))
		top := int(height * a.config.CropSettings.TopPercent)
		bottom := int(height * (1.0 - a.config.CropSettings.BottomPercent))

		targetBounds = image.Rect(left, top, right, bottom)
		fmt.Printf("[异步处理] 使用配置裁剪区域: (%d,%d) 到 (%d,%d)\n", left, top, right, bottom)
	} else {
		// 如果没有配置，使用全屏
		targetBounds = fullBounds
		fmt.Printf("[异步处理] 配置未加载，使用全屏截图\n")
	}

	// 2. 直接截取目标区域，避免二次裁剪
	img, err := screenshot.CaptureRect(targetBounds)
	if err != nil {
		a.sendTaskErrorEvent(taskID, fmt.Sprintf("截图失败: %v", err))
		return
	}

	// 3. 保存临时截图文件
	timestamp := time.Now().Format("20060102_150405")
	tempFileName := fmt.Sprintf("temp_screenshot_auto_%s_%s.png", userName, timestamp)

	// 获取当前患者报到号
	registrationNumber := a.getCurrentPatientRegistrationNumber()

	// 生成患者目录名：受检者名称+报到号
	patientDir := fmt.Sprintf("%s+%s", userName, registrationNumber)
	tempFilePath := filepath.Join("./pic/temp", patientDir, tempFileName)

	// 确保目录存在
	if err := os.MkdirAll(filepath.Dir(tempFilePath), 0755); err != nil {
		a.sendTaskErrorEvent(taskID, fmt.Sprintf("创建目录失败: %v", err))
		return
	}

	// 保存裁剪后的截图
	file, err := os.Create(tempFilePath)
	if err != nil {
		a.sendTaskErrorEvent(taskID, fmt.Sprintf("创建文件失败: %v", err))
		return
	}
	defer file.Close()

	if err := png.Encode(file, img); err != nil {
		a.sendTaskErrorEvent(taskID, fmt.Sprintf("保存截图失败: %v", err))
		return
	}

	fmt.Printf("[异步处理] 截图保存成功: %s\n", tempFilePath)

	// 4. 进行OCR识别
	if a.ocrService == nil {
		a.sendTaskErrorEvent(taskID, "OCR服务未初始化")
		return
	}

	fmt.Printf("[异步处理] 开始OCR识别...\n")
	ocrResult, err := a.ocrService.ProcessImageWithDetails(context.Background(), tempFilePath)
	if err != nil {
		a.sendTaskErrorEvent(taskID, fmt.Sprintf("OCR识别失败: %v", err))
		return
	}

	fmt.Printf("[异步处理] OCR识别完成，器官名称: %s\n", ocrResult.OrganName)

	// 5. 根据OCR结果中的数字字段判断模式
	detectedMode := a.detectModeFromOCRResult(ocrResult)
	fmt.Printf("[异步处理] 检测到模式: %s\n", detectedMode)

	// 6. 根据检测到的模式进行相应处理
	var finalResult map[string]interface{}
	switch detectedMode {
	case "B02":
		finalResult, err = a.processBtnScreenshotTask(taskID, userName, "B", tempFilePath, ocrResult)
	case "C03":
		finalResult, err = a.processBtnScreenshotTask(taskID, userName, "C", tempFilePath, ocrResult)
	default:
		a.sendTaskErrorEvent(taskID, "无法确定截图模式，OCR结果不符合预期格式")
		return
	}

	if err != nil {
		a.sendTaskErrorEvent(taskID, fmt.Sprintf("处理截图任务失败: %v", err))
		return
	}

	// 7. 构建最终结果
	result := map[string]interface{}{
		"success":           true,
		"task_id":           taskID,
		"user_name":         userName,
		"detected_mode":     detectedMode,
		"organ_name":        ocrResult.OrganName,
		"confidence":        ocrResult.Confidence,
		"image_path":        tempFilePath,
		"ocr_result":        ocrResult,
		"processing_result": finalResult,
		"timestamp":         time.Now().Format("2006-01-02 15:04:05"),
	}

	// 8. 发送任务完成事件
	runtime.EventsEmit(a.ctx, "task-completed", map[string]interface{}{
		"task_id":   taskID,
		"task_type": "button_screenshot",
		"status":    "completed",
		"result":    result,
	})

	// 只在调试模式下打印函数执行结果
	if a.config != nil && a.config.Debug {
		fmt.Printf("[异步处理] 任务完成 - TaskID: %s, UserName: %s, Mode: %s, OrganName: %s, Confidence: %.2f, ImagePath: %s\n",
			taskID, userName, detectedMode, ocrResult.OrganName, ocrResult.Confidence, tempFilePath)
	}
}

// sendTaskErrorEvent 发送任务错误事件
func (a *App) sendTaskErrorEvent(taskID, errorMsg string) {
	// 只在调试模式下输出错误信息
	if a.config != nil && a.config.Debug {
		fmt.Printf("[异步处理] 任务失败 - TaskID: %s, 错误: %s\n", taskID, errorMsg)
	}

	runtime.EventsEmit(a.ctx, "task-error", map[string]interface{}{
		"task_id":   taskID,
		"task_type": "button_screenshot",
		"status":    "failed",
		"error":     errorMsg,
		"timestamp": time.Now().Unix(),
	})

	// 同时发送Toast通知
	a.ShowWailsNotification("error", "智能截图任务失败", errorMsg, 5000)
}

// detectModeFromOCRResult 根据OCR结果中的数字字段判断B02或C03模式
func (a *App) detectModeFromOCRResult(ocrResult *services.OCRResult) string {
	if ocrResult == nil {
		return "B02" // 默认返回B02
	}

	// 统计数字分布来判断模式
	var smallNumbers []float64 // 0.xxx 格式的数字
	var largeNumbers []float64 // >= 1.0 的数字
	var allNumbers []float64   // 所有数字

	// 首先检查KeyValuePairs中的数字字段
	if ocrResult.KeyValuePairs != nil {
		fmt.Printf("[模式检测] 检查KeyValuePairs，共%d个键值对\n", len(ocrResult.KeyValuePairs))
		for key, value := range ocrResult.KeyValuePairs {
			// 检查是否为数字格式的键
			if strings.Contains(key, ".") {
				// 解析数字
				if num, err := strconv.ParseFloat(key, 64); err == nil {
					fmt.Printf("[模式检测] 发现数字字段: %s = %s (数值: %.3f)\n", key, value, num)

					// 排除用于器官名称识别的特定数字"0.000"
					if key == "0.000" {
						fmt.Printf("[模式检测] 跳过器官名称识别键: %s = %s\n", key, value)
						continue
					}

					allNumbers = append(allNumbers, num)
					if num >= 1.0 {
						largeNumbers = append(largeNumbers, num)
					} else if num > 0.0 {
						smallNumbers = append(smallNumbers, num)
					}
				}
			}
		}
	}

	// 如果KeyValuePairs没有足够数据，尝试从原始响应数据中查找
	if len(allNumbers) < 5 && ocrResult.RawResponse != nil {
		fmt.Printf("[模式检测] KeyValuePairs数据不足(%d个数字)，检查原始响应数据\n", len(allNumbers))

		// 解析原始响应数据
		var rawMap map[string]interface{}
		if err := json.Unmarshal(ocrResult.RawResponse, &rawMap); err == nil {
			// 递归查找rec_texts数组
			if recTexts := a.findRecTextsInResponse(rawMap); len(recTexts) > 0 {
				fmt.Printf("[模式检测] 从原始响应中找到rec_texts，共%d个文本\n", len(recTexts))

				// 检查rec_texts中的数字（干扰数据已在OCR提取阶段被清理）
				for _, text := range recTexts {
					if strings.Contains(text, ".") {
						if num, err := strconv.ParseFloat(text, 64); err == nil {
							fmt.Printf("[模式检测] 原始数据中发现数字: %s (数值: %.3f)\n", text, num)
							allNumbers = append(allNumbers, num)
							if num >= 1.0 {
								largeNumbers = append(largeNumbers, num)
							} else if num > 0.0 {
								smallNumbers = append(smallNumbers, num)
							}

							// 如果已经收集到足够的数字样本，提前退出
							if len(allNumbers) >= 10 {
								break
							}
						}
					}
				}
			}
		}
	}

	// 基于数字分布模式进行判断
	fmt.Printf("[模式检测] 数字统计: 总数=%d, 小数字(0-1)=%d, 大数字(>=1)=%d\n",
		len(allNumbers), len(smallNumbers), len(largeNumbers))

	if len(allNumbers) == 0 {
		fmt.Printf("[模式检测] 未找到任何数字，默认使用B02模式\n")
		return "B02"
	}

	// 判断规则：
	// 1. 如果大数字(>=1)的比例超过30%，判断为C03
	// 2. 如果大数字数量>=3个，判断为C03
	// 3. 否则判断为B02
	largeRatio := float64(len(largeNumbers)) / float64(len(allNumbers))
	fmt.Printf("[模式检测] 大数字比例: %.2f%% (%d/%d)\n", largeRatio*100, len(largeNumbers), len(allNumbers))

	if largeRatio >= 0.3 || len(largeNumbers) >= 3 {
		fmt.Printf("[模式检测] 大数字比例较高或数量较多，判断为C03模式\n")
		return "C03"
	} else {
		fmt.Printf("[模式检测] 小数字占主导，判断为B02模式\n")
		return "B02"
	}
}

// findRecTextsInResponse 递归查找响应数据中的rec_texts数组
func (a *App) findRecTextsInResponse(data interface{}) []string {
	switch v := data.(type) {
	case map[string]interface{}:
		// 检查当前层级是否有rec_texts
		if recTexts, exists := v["rec_texts"]; exists {
			if texts, ok := recTexts.([]interface{}); ok {
				result := make([]string, 0, len(texts))
				for _, text := range texts {
					if str, ok := text.(string); ok {
						result = append(result, str)
					}
				}
				fmt.Printf("[模式检测] 找到rec_texts数组，包含%d个文本\n", len(result))
				return result
			}
		}

		// 递归搜索子对象
		for _, value := range v {
			if result := a.findRecTextsInResponse(value); len(result) > 0 {
				return result
			}
		}

	case []interface{}:
		// 递归搜索数组元素
		for _, item := range v {
			if result := a.findRecTextsInResponse(item); len(result) > 0 {
				return result
			}
		}
	}

	return nil
}

// processBtnScreenshotTask 处理按钮触发的截图任务
func (a *App) processBtnScreenshotTask(taskID, userName, mode, imagePath string, ocrResult *services.OCRResult) (map[string]interface{}, error) {
	fmt.Printf("[任务处理] 开始处理按钮截图任务 - ID: %s, 模式: %s\n", taskID, mode)
	fmt.Printf("[调试] 任务包括：1.重命名截图文件（带截图B/C模式文件名）并保存到pic目录；2.更新当前受检者检测信息变量；3.如果是B模式则进行颜色检测；4.发送通知到前端界面\n")

	// 1. 重命名文件以包含模式信息
	timestamp := time.Now().Format("20060102_150405")
	finalFileName := fmt.Sprintf("%s_BTN_%s_%s_%s.png", userName, mode, taskID[len(taskID)-8:], timestamp)
	finalFilePath := filepath.Join("./pic", finalFileName)

	// 复制文件到最终位置
	if err := a.copyFile(imagePath, finalFilePath); err != nil {
		return nil, fmt.Errorf("复制文件失败: %v", err)
	}

	fmt.Printf("[任务处理] 文件已保存到: %s\n", finalFilePath)

	// 2. 更新用户检测信息
	currentUser := a.getCurrentUserRegistration()
	if currentUser == nil {
		fmt.Printf("[警告] 未找到当前用户报到信息\n")
	}

	// 3. 调用现有的更新流程
	err := a.updateCurrentUserCheckingInfo(userName, mode, finalFilePath, ocrResult.OrganName, ocrResult, nil, currentUser)
	if err != nil {
		return nil, fmt.Errorf("更新用户检测信息失败: %v", err)
	} else {
		fmt.Printf("[调试] 调用updateCurrentUserCheckingInfo方法更新当前受检者检测信息变量（未来融合好20个截图数据后上传AI大模型用）\n")

	}

	// 4. 如果是B02模式，进行颜色检测
	var colorDetectionResult map[string]interface{}
	if mode == "B" || mode == "B02" {
		fmt.Printf("[任务处理] B02模式，开始颜色检测...\n")
		if len(ocrResult.RawResponse) > 0 {
			colorResult, colorErr := services.ProcessImageColorDetection(finalFilePath, ocrResult.RawResponse, a.config)
			if colorErr != nil {
				fmt.Printf("[警告] 颜色检测失败: %v\n", colorErr)
			} else {
				colorDetectionResult = colorResult
				fmt.Printf("[任务处理] 颜色检测完成\n")
			}
		}
	}

	// 5. 发送通知
	modeDesc := "B02生化平衡分析"
	if mode == "C" || mode == "C03" {
		modeDesc = "C03病理形态学分析"
	}

	a.ShowSuccessNotification(
		"模式分析完成",
		fmt.Sprintf("%s截图和OCR识别已完成\n器官: %s\n置信度: %.1f%%", modeDesc, ocrResult.OrganName, ocrResult.Confidence*100),
		5000,
	)

	return map[string]interface{}{
		"success":              true,
		"task_id":              taskID,
		"mode":                 mode,
		"mode_description":     modeDesc,
		"final_file_path":      finalFilePath,
		"organ_name":           ocrResult.OrganName,
		"confidence":           ocrResult.Confidence,
		"color_detection":      colorDetectionResult,
		"processing_timestamp": time.Now().Format("2006-01-02 15:04:05"),
	}, nil
}

// copyFile 复制文件的辅助函数
func (a *App) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	// 确保目标目录存在
	if err := os.MkdirAll(filepath.Dir(dst), 0755); err != nil {
		return err
	}

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	return err
}

// getCurrentUserRegistration 获取当前用户报到信息
func (a *App) getCurrentUserRegistration() *models.Registration {
	// 获取当前患者名称
	userName := a.getCurrentPatientName()
	if userName == "" {
		return nil
	}

	// 尝试从患者服务获取报到信息
	if a.patientService != nil {
		// 这里可以根据需要实现具体的获取逻辑
		// 目前返回nil，表示没有找到报到信息
		fmt.Printf("[DEBUG] 尝试获取用户 %s 的报到信息\n", userName)
	}

	// 返回nil表示未找到或不需要报到信息
	return nil
}

// ========== Wails现代化通知方法 ==========

// ShowWailsNotification 显示通知（使用置顶信息窗口和第二窗口）
// 统一使用主窗体Toast通知显示通知
func (a *App) ShowWailsNotification(notificationType, title, message string, duration int) string {
	// 使用主窗体Toast通知显示
	a.ShowToastNotification(title, message, notificationType, duration, false, 0)

	// 只在调试模式下输出到控制台
	if a.config != nil && a.config.Debug {
		fmt.Printf("[%s] %s: %s\n", notificationType, title, message)
	}
	return "toast-notification-sent"
}

// ShowSuccessNotification 显示成功通知
func (a *App) ShowSuccessNotification(title, message string, duration int) string {
	return a.ShowWailsNotification("success", title, message, duration)
}

// ShowErrorNotification 显示错误通知
func (a *App) ShowErrorNotification(title, message string, duration int) string {
	return a.ShowWailsNotification("error", title, message, duration)
}

// ShowWarningNotification 显示警告通知
func (a *App) ShowWarningNotification(title, message string, duration int) string {
	return a.ShowWailsNotification("warning", title, message, duration)
}

// ShowInfoNotification 显示信息通知
func (a *App) ShowInfoNotification(title, message string, duration int) string {
	return a.ShowWailsNotification("info", title, message, duration)
}

// ShowProgressNotification 显示进度通知
func (a *App) ShowProgressNotification(title, message string, progress int, duration int) string {
	progressMessage := fmt.Sprintf("%s (%d%%)", message, progress)

	// 发送进度通知事件到前端主窗口
	runtime.EventsEmit(a.ctx, "showProcessingNotification", map[string]interface{}{
		"organName":   title,
		"currentStep": progress,
		"totalSteps":  100,
		"message":     progressMessage,
	})

	// 备用方案：控制台输出
	fmt.Printf("[进度] %s: %s (%d%%)\n", title, message, progress)
	return "main-window-progress-sent"
}

// UpdateProgressNotification 更新进度通知
func (a *App) UpdateProgressNotification(id string, progress int, message string) {
	// 发送进度更新事件到前端
	runtime.EventsEmit(a.ctx, "wails:update-progress", map[string]interface{}{
		"progress": progress,
	})
}

// 注释：已移除updateOrganNameInOCRData方法，因为器官名称已在OCR处理阶段完成校验，无需重复处理

// ShowOCRProcessNotification 显示OCR处理通知（带进度）
func (a *App) ShowOCRProcessNotification(title, message string, progress int) {
	a.ShowProgressNotification(title, message, progress, 0) // 0表示不自动消失
}

// ShowToastNotification 显示Toast通知到前端
func (a *App) ShowToastNotification(title, message, notificationType string, duration int, showProgress bool, progress int) {
	// 发送Toast通知事件到前端
	runtime.EventsEmit(a.ctx, "showToastNotification", map[string]interface{}{
		"title":        title,
		"message":      message,
		"type":         notificationType,
		"duration":     duration,
		"showProgress": showProgress,
		"progress":     progress,
	})

	// 控制台输出
	fmt.Printf("[Toast] %s: %s\n", title, message)
}

// ========== 消息配置系统方法 ==========

// UpdateOperationStatus 更新前端操作状态显示
func (a *App) UpdateOperationStatus(mode string, isCompleted bool) {
	status := utils.GetOperationStatus(mode, isCompleted)

	// 发送状态更新事件到前端
	runtime.EventsEmit(a.ctx, "updateOperationStatus", map[string]interface{}{
		"status":    status,
		"mode":      mode,
		"completed": isCompleted,
	})

	fmt.Printf("[操作状态] %s\n", status)
	utils.LogInfo("更新操作状态", zap.String("status", status), zap.String("mode", mode), zap.Bool("completed", isCompleted))
}

// SendConfigurableToast 发送可配置的Toast消息
func (a *App) SendConfigurableToast(category, messageType string, params map[string]interface{}) {
	message := utils.GetToastMessage(category, messageType, params)

	// 根据类别确定Toast类型
	var toastType string
	switch category {
	case "success":
		toastType = "success"
	case "error":
		toastType = "error"
	case "warning":
		toastType = "warning"
	case "progress":
		toastType = "info"
	default:
		toastType = "info"
	}

	// 发送Toast通知
	a.ShowToastNotification("系统提示", message, toastType, 4000, false, 0)
}

// UpdateProgressBar 更新进度条
func (a *App) UpdateProgressBar(completedTasks int, totalTasks int, mode string) {
	progress := float64(completedTasks) / float64(totalTasks) * 100
	color := utils.GetProgressBarColor(mode)

	// 发送进度更新事件到前端
	runtime.EventsEmit(a.ctx, "updateProgress", map[string]interface{}{
		"progress":        progress,
		"color":           color,
		"mode":            mode,
		"completed_tasks": completedTasks,
		"total_tasks":     totalTasks,
	})

	fmt.Printf("[进度更新] %.1f%% - 模式: %s, 已完成任务: %d/%d\n", progress, mode, completedTasks, totalTasks)
}

// SetCompactWindow 设置窗口为紧凑模式
func (a *App) SetCompactWindow() {
	if a.ctx == nil {
		return
	}

	// 获取屏幕尺寸
	screens, err := runtime.ScreenGetAll(a.ctx)
	if err != nil || len(screens) == 0 {
		return
	}

	primaryScreen := screens[0]
	screenWidth := primaryScreen.Size.Width
	screenHeight := primaryScreen.Size.Height

	// 使用紧凑模式的配置
	if a.config != nil {
		x := int(float64(screenWidth) * a.config.NormalWindowsSetting.LeftPercent)
		y := int(float64(screenHeight) * a.config.NormalWindowsSetting.TopPercent)

		windowWidth := int(float64(screenWidth) * (a.config.NormalWindowsSetting.RightPercent - a.config.NormalWindowsSetting.LeftPercent))
		windowHeight := int(float64(screenHeight) * (a.config.NormalWindowsSetting.BottomPercent - a.config.NormalWindowsSetting.TopPercent))

		runtime.WindowSetPosition(a.ctx, x, y)
		runtime.WindowSetSize(a.ctx, windowWidth, windowHeight)

		utils.LogInfo(fmt.Sprintf("窗口已切换到紧凑模式: x=%d, y=%d, width=%d, height=%d", x, y, windowWidth, windowHeight))
	}
}

// SetExpandedWindow 设置窗口为展开模式
func (a *App) SetExpandedWindow() {
	if a.ctx == nil {
		return
	}

	// 获取屏幕尺寸
	screens, err := runtime.ScreenGetAll(a.ctx)
	if err != nil || len(screens) == 0 {
		return
	}

	primaryScreen := screens[0]
	screenWidth := primaryScreen.Size.Width
	screenHeight := primaryScreen.Size.Height

	// 使用展开模式的配置
	if a.config != nil {
		x := int(float64(screenWidth) * a.config.ExpandedCropSettings.LeftPercent)
		y := int(float64(screenHeight) * a.config.ExpandedCropSettings.TopPercent)

		windowWidth := int(float64(screenWidth) * (a.config.ExpandedCropSettings.RightPercent - a.config.ExpandedCropSettings.LeftPercent))
		windowHeight := int(float64(screenHeight) * (a.config.ExpandedCropSettings.BottomPercent - a.config.ExpandedCropSettings.TopPercent))

		runtime.WindowSetPosition(a.ctx, x, y)
		runtime.WindowSetSize(a.ctx, windowWidth, windowHeight)

		utils.LogInfo(fmt.Sprintf("窗口已切换到展开模式: x=%d, y=%d, width=%d, height=%d", x, y, windowWidth, windowHeight))
	}
}

// TakeConcurrentScreenshot 执行并发截图 - 已简化为使用SimpleScreenshotManager
func (a *App) TakeConcurrentScreenshot(mode string) (interface{}, error) {
	if a.simpleScreenshotManager == nil {
		return nil, fmt.Errorf("simple screenshot manager not initialized")
	}
	// 使用简化的截图管理器执行截图
	err := a.simpleScreenshotManager.TakeScreenshot("default_user", mode)
	if err != nil {
		return nil, err
	}
	// 生成简单的任务ID
	taskID := fmt.Sprintf("%s_%d", mode, time.Now().UnixNano())
	return map[string]interface{}{
		"success": true,
		"message": "截图任务已提交",
		"mode":    mode,
		"task_id": taskID,
	}, nil
}

// Greet returns a greeting for the given name
func (a *App) Greet(name string) string {
	return fmt.Sprintf("Hello %s, It's show time!", name)
}

// checkOCREnvironmentOnStartup 在应用启动时检测OCR环境
func (a *App) checkOCREnvironmentOnStartup() {
	if a.ocrService == nil {
		utils.LogWarning("OCR服务未初始化，跳过环境检测")
		return
	}

	// 检测网络连通性（只测试baidu.com）
	if !a.checkNetworkConnectivityOnStartup() {
		utils.LogError("OCR环境检测", "", fmt.Errorf("网络连接检测失败"))
		a.ShowWarningNotification("网络连接异常", "无法连接到网络，OCR功能可能无法正常使用", 8000)
		return
	}

	// 检测OCR API可用性
	if !a.checkOCRAPIAvailabilityOnStartup() {
		utils.LogError("OCR环境检测", "", fmt.Errorf("OCR API连接检测失败"))
		a.ShowWarningNotification("OCR服务异常", "无法连接到OCR服务，请检查网络连接或稍后重试", 8000)
		return
	}

	utils.LogInfo("OCR环境检测通过")
	a.ShowInfoNotification("系统就绪", "数据提取环境检测正常，系统已准备就绪", 3000)
}

// checkNetworkConnectivityOnStartup 启动时检测网络连通性（只测试baidu.com）
func (a *App) checkNetworkConnectivityOnStartup() bool {
	client := utils.NewFastHTTPClient(5 * time.Second) // 5秒超时

	// 只测试baidu.com
	testURL := "https://www.baidu.com"
	utils.LogInfo("正在测试网络连接")

	resp, err := client.Get(testURL, nil)
	if err != nil {
		utils.LogError("网络连接测试", "", fmt.Errorf("连接失败: %s, 错误: %v", testURL, err))
		return false
	}
	defer fasthttp.ReleaseResponse(resp)

	statusCode := resp.StatusCode()
	if statusCode >= 200 && statusCode < 300 {
		utils.LogInfo(fmt.Sprintf("网络连接测试成功: %s, 状态码: %d", testURL, statusCode))
		return true
	}

	utils.LogWarning(fmt.Sprintf("网络连接测试返回异常状态码: %s, 状态码: %d", testURL, statusCode))
	return false
}

// checkOCRAPIAvailabilityOnStartup 启动时检测OCR API可用性
func (a *App) checkOCRAPIAvailabilityOnStartup() bool {
	if a.config == nil {
		utils.LogError("OCR API配置检查", "", fmt.Errorf("应用配置未加载"))
		return false
	}

	// 检查火山引擎OCR配置
	volcEngineConfigured := a.config.APIKeys.VolcEngineOCR.APIURL != "" &&
		a.config.APIKeys.VolcEngineOCR.AccessKeyID != "" &&
		a.config.APIKeys.VolcEngineOCR.SecretAccessKey != ""

	// 检查百度OCR配置
	baiduConfigured := a.config.APIKeys.OCR.APIURL != "" && a.config.APIKeys.OCR.Token != ""

	if !volcEngineConfigured && !baiduConfigured {
		utils.LogError("OCR API配置检查", "", fmt.Errorf("未找到任何可用的OCR配置（火山引擎OCR或百度OCR）"))
		return false
	}

	// 优先检查火山引擎OCR
	if volcEngineConfigured {
		utils.LogInfo("检测到火山引擎OCR配置，OCR服务可用")
		return true
	}

	// 检查百度OCR连接
	if baiduConfigured {
		client := utils.NewFastHTTPClient(10 * time.Second) // 10秒超时
		apiURL := a.config.APIKeys.OCR.APIURL
		utils.LogInfo(fmt.Sprintf("正在测试百度OCR API连接: %s", apiURL))

		resp, err := client.Head(apiURL, nil)
		if err != nil {
			utils.LogWarning(fmt.Sprintf("百度OCR API连接测试失败: %s, 错误: %v，但配置存在", apiURL, err))
			return true // 配置存在就认为可用，网络问题可能是临时的
		}
		defer fasthttp.ReleaseResponse(resp)

		statusCode := resp.StatusCode()
		// 检查状态码：200-299表示正常，403/405表示服务可达但需要认证或方法限制
		if (statusCode >= 200 && statusCode < 300) || statusCode == 403 || statusCode == 405 {
			utils.LogInfo(fmt.Sprintf("百度OCR API连接测试成功: %s, 状态码: %d", apiURL, statusCode))
			return true
		}

		utils.LogWarning(fmt.Sprintf("百度OCR API连接测试返回异常状态码: %s, 状态码: %d，但配置存在", apiURL, statusCode))
		return true // 配置存在就认为可用
	}

	return false
}
