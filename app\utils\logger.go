package utils

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Logger 全局日志记录器
var Logger *zap.Logger

// Sugar 全局Sugar日志记录器，提供更友好的API
var Sugar *zap.SugaredLogger

// InitLogger 初始化全局日志记录器
func InitLogger() error {
	// 在开发环境中使用ERROR级别，进一步减少控制台输出
	logger, err := NewLogger(zapcore.ErrorLevel)
	if err != nil {
		return err
	}
	Logger = logger
	return nil
}

// NewLogger 创建新的生产环境日志记录器
func NewLogger(level zapcore.Level) (*zap.Logger, error) {
	// 创建日志目录 - 使用当前工作目录
	workDir, err := os.Getwd()
	if err != nil {
		return nil, fmt.Errorf("获取当前工作目录失败: %v", err)
	}
	logDir := filepath.Join(workDir, "logs")
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return nil, fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 创建日志文件
	logFileName := fmt.Sprintf("app_%s.log", time.Now().Format("2006-01-02"))
	logFilePath := filepath.Join(logDir, logFileName)

	// 使用生产环境配置
	config := zap.NewProductionConfig()
	config.Level = zap.NewAtomicLevelAt(level)
	// 只输出到文件，不输出到控制台以减少终端噪音
	config.OutputPaths = []string{logFilePath}
	config.ErrorOutputPaths = []string{logFilePath}

	// 自定义时间格式
	config.EncoderConfig.TimeKey = "timestamp"
	config.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	config.EncoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	// 构建日志记录器
	logger, err := config.Build(zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))
	if err != nil {
		return nil, fmt.Errorf("构建日志记录器失败: %v", err)
	}

	// 初始化Sugar日志记录器
	Sugar = logger.Sugar()

	return logger, nil
}

// LogOperation 记录操作日志，使用结构化字段
func LogOperation(operation, curPatientName, siteID string) {
	if Logger != nil {
		Logger.Info("操作记录",
			zap.String("operation", operation),
			zap.String("patient", curPatientName),
			zap.String("site_id", siteID),
		)
	}
}

// LogError 记录错误日志
func LogError(operation, curPatientName string, err error) {
	if Logger != nil {
		Logger.Error("操作错误",
			zap.String("operation", operation),
			zap.String("patient", curPatientName),
			zap.Error(err),
		)
	}
}

// LogInfo 记录信息日志
func LogInfo(message string, fields ...zap.Field) {
	if Logger != nil {
		Logger.Info(message, fields...)
	}
}

// LogWarning 记录警告日志
func LogWarning(message string, fields ...zap.Field) {
	if Logger != nil {
		Logger.Warn(message, fields...)
	}
}

// LogDebug 记录调试日志
func LogDebug(message string, fields ...zap.Field) {
	if Logger != nil {
		Logger.Debug(message, fields...)
	}
}

// Sync 刷新缓冲的日志条目
func Sync() {
	if Logger != nil {
		Logger.Sync()
	}
}

// 便捷的Sugar API函数

// Infof 使用格式化字符串记录信息日志
func Infof(template string, args ...interface{}) {
	if Sugar != nil {
		Sugar.Infof(template, args...)
	}
}

// Errorf 使用格式化字符串记录错误日志
func Errorf(template string, args ...interface{}) {
	if Sugar != nil {
		Sugar.Errorf(template, args...)
	}
}

// Warnf 使用格式化字符串记录警告日志
func Warnf(template string, args ...interface{}) {
	if Sugar != nil {
		Sugar.Warnf(template, args...)
	}
}

// Debugf 使用格式化字符串记录调试日志
func Debugf(template string, args ...interface{}) {
	if Sugar != nil {
		Sugar.Debugf(template, args...)
	}
}

// Infow 使用键值对记录信息日志
func Infow(msg string, keysAndValues ...interface{}) {
	if Sugar != nil {
		Sugar.Infow(msg, keysAndValues...)
	}
}

// Errorw 使用键值对记录错误日志
func Errorw(msg string, keysAndValues ...interface{}) {
	if Sugar != nil {
		Sugar.Errorw(msg, keysAndValues...)
	}
}

// Warnw 使用键值对记录警告日志
func Warnw(msg string, keysAndValues ...interface{}) {
	if Sugar != nil {
		Sugar.Warnw(msg, keysAndValues...)
	}
}

// Debugw 使用键值对记录调试日志
func Debugw(msg string, keysAndValues ...interface{}) {
	if Sugar != nil {
		Sugar.Debugw(msg, keysAndValues...)
	}
}
