<template>
  <!-- 任务管理器状态弹窗遮罩 -->
  <div v-if="isVisible" class="task-manager-overlay" @click="handleOverlayClick">
    <div class="task-manager-modal" @click.stop>
          <!-- 弹窗头部 -->
          <div class="modal-header">
            <h3 class="modal-title">任务管理器状态</h3>
            <button class="close-btn" @click="handleClose" title="关闭">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>
              </svg>
            </button>
          </div>

      <!-- 弹窗内容 -->
      <div class="modal-content">
        <!-- 统计信息网格 -->
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-label">活跃任务</div>
            <div class="stat-value">{{ taskStats.active_count || 0 }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">队列大小</div>
            <div class="stat-value">{{ taskStats.queue_size || 0 }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">总提交</div>
            <div class="stat-value">{{ taskStats.total_submitted || 0 }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">已完成</div>
            <div class="stat-value">{{ taskStats.completed_tasks || 0 }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">失败任务</div>
            <div class="stat-value">{{ taskStats.failed_tasks || 0 }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">已取消</div>
            <div class="stat-value">{{ taskStats.cancelled_tasks || 0 }}</div>
          </div>
        </div>

        <!-- 活跃任务列表 -->
        <div class="active-tasks-section">
          <h4 class="section-title">活跃任务列表</h4>
          <div class="active-tasks-container">
            <div v-if="activeTasks.length === 0" class="no-tasks">
              当前无活跃任务
            </div>
            <div v-else>
              <div v-for="task in activeTasks" :key="task.id" class="task-item">
                <div class="task-header">
                  <span class="task-id">{{ task.id }}</span>
                  <span class="task-type">{{ getTaskTypeLabel(task.type) }}</span>
                  <span class="task-status">{{ getTaskStatusLabel(task.status) }}</span>
                </div>
                <div class="task-details">
                  <p><strong>用户:</strong> {{ task.user_name || 'N/A' }}</p>
                  <p><strong>模式:</strong> {{ task.mode || 'N/A' }}</p>
                  <p><strong>开始时间:</strong> {{ formatTime(task.started_at) }}</p>
                  <p><strong>优先级:</strong> {{ task.priority || 'N/A' }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskManagerStatus',
  props: {
    isVisible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close'],
  data() {
    return {
      taskStats: {
        active_count: 0,
        queue_size: 0,
        total_submitted: 0,
        completed_tasks: 0,
        failed_tasks: 0,
        cancelled_tasks: 0
      },
      activeTasks: [],
      updateTimer: null
    }
  },
  watch: {
    isVisible(newVal, oldVal) {
      console.log('[TaskManagerStatus] isVisible 变化:', oldVal, '->', newVal)
      if (newVal) {
        this.startUpdating()
        this.$nextTick(() => {
          // 添加键盘事件监听
          document.addEventListener('keydown', this.handleKeydown)
        })
      } else {
        this.stopUpdating()
        document.removeEventListener('keydown', this.handleKeydown)
      }
    }
  },
  mounted() {
    console.log('[TaskManagerStatus] 组件已挂载')
  },
  beforeUnmount() {
    console.log('[TaskManagerStatus] 组件即将卸载')
    this.stopUpdating()
    document.removeEventListener('keydown', this.handleKeydown)
  },
  methods: {
    handleOverlayClick() {
      console.log('[TaskManagerStatus] 遮罩点击，关闭弹窗')
      this.handleClose()
    },

    handleClose() {
      console.log('[TaskManagerStatus] 关闭按钮点击，发送close事件')
      this.$emit('close')
    },

    handleKeydown(event) {
      if (event.key === 'Escape') {
        console.log('[TaskManagerStatus] ESC键按下，关闭弹窗')
        this.handleClose()
      }
    },

    async updateTaskStatus() {
      try {
        console.log('[TaskManagerStatus] 开始更新任务状态')
        if (window.go && window.go.main && window.go.main.App && window.go.main.App.GetTaskManagerStatus) {
          const status = await window.go.main.App.GetTaskManagerStatus()
          console.log('[TaskManagerStatus] 获取到任务状态:', status)
          if (status) {
            this.taskStats = status.stats || {}
            this.activeTasks = status.active_tasks || []
            console.log('[TaskManagerStatus] 任务统计已更新:', this.taskStats)
          }
        } else {
          console.log('[TaskManagerStatus] Wails API不可用')
        }
      } catch (error) {
        console.error('[TaskManagerStatus] 获取任务状态失败:', error)
      }
    },

    startUpdating() {
      console.log('[TaskManagerStatus] 开始定时更新任务状态')
      this.updateTaskStatus() // 立即更新一次
      if (this.updateTimer) {
        clearInterval(this.updateTimer)
      }
      this.updateTimer = setInterval(() => {
        console.log('[TaskManagerStatus] 定时更新任务状态')
        this.updateTaskStatus()
      }, 30000) // 每30秒更新一次，减少频繁调用
    },

    stopUpdating() {
      console.log('[TaskManagerStatus] 停止定时更新任务状态')
      if (this.updateTimer) {
        clearInterval(this.updateTimer)
        this.updateTimer = null
      }
    },

    getTaskTypeLabel(type) {
      const typeMap = {
        'screenshot_A': '器官问题分析',
        'screenshot_B': '生化平衡分析', 
        'screenshot_C': '病理形态分析',
        'ocr_process': 'OCR处理',
        'upload': '上传任务'
      }
      return typeMap[type] || type
    },

    getTaskStatusLabel(status) {
      const statusMap = {
        'pending': '等待中',
        'running': '执行中',
        'completed': '已完成',
        'failed': '失败',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    },

    formatTime(timestamp) {
      if (!timestamp) return 'N/A'
      try {
        const date = new Date(timestamp)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      } catch (err) {
        return 'Invalid Date'
      }
    }
  }
}
</script>

<style scoped>
/* 遮罩层 */
.task-manager-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
}

/* 弹窗主体 */
.task-manager-modal {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow: hidden;
  color: white;
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 弹窗头部 */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: all 0.2s ease;
  outline: none;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.close-btn:focus {
  background: rgba(255, 255, 255, 0.4);
  outline: 2px solid rgba(255, 255, 255, 0.5);
}

/* 弹窗内容 */
.modal-content {
  padding: 24px;
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}

/* 统计信息网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #fff;
}

/* 活跃任务部分 */
.active-tasks-section {
  margin-top: 24px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffd700;
  border-bottom: 1px solid rgba(255, 215, 0, 0.3);
  padding-bottom: 8px;
}

.active-tasks-container {
  max-height: 300px;
  overflow-y: auto;
}

.no-tasks {
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
  padding: 24px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.task-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.task-header {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.task-id {
  font-family: monospace;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}

.task-type {
  background: rgba(52, 211, 153, 0.3);
  color: #10b981;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
}

.task-status {
  background: rgba(251, 191, 36, 0.3);
  color: #f59e0b;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
}

.task-details p {
  margin: 4px 0;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
}

.task-details strong {
  color: white;
}

/* 滚动条样式 */
.modal-content::-webkit-scrollbar,
.active-tasks-container::-webkit-scrollbar {
  width: 6px;
}

.modal-content::-webkit-scrollbar-track,
.active-tasks-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb,
.active-tasks-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb:hover,
.active-tasks-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-manager-modal {
    width: 95%;
    margin: 10px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .modal-header {
    padding: 16px 20px;
  }
  
  .modal-content {
    padding: 20px;
  }
}
</style>
