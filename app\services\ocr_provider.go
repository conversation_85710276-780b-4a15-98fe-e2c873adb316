// Package services 提供统一的OCR服务抽象层
package services

import (
	"context"
	"fmt"
)

// OCRProvider OCR服务提供者接口
// 定义了所有OCR服务必须实现的基本方法
type OCRProvider interface {
	// ProcessImage 处理图片并返回OCR结果
	ProcessImage(ctx context.Context, imagePath string) (*OCRResult, error)

	// GetProviderName 获取提供者名称
	GetProviderName() string

	// ValidateConfig 验证配置是否有效
	ValidateConfig() error

	// Close 关闭连接和清理资源
	Close()
}

// OCRProviderType OCR提供者类型
type OCRProviderType string

const (
	// ProviderBaidu 百度OCR
	ProviderBaidu OCRProviderType = "baidu"
	// ProviderVolcEngine 火山引擎OCR
	ProviderVolcEngine OCRProviderType = "volcengine"
	// ProviderTencent 腾讯OCR (预留)
	ProviderTencent OCRProviderType = "tencent"
	// ProviderAliyun 阿里云OCR (预留)
	ProviderAliyun OCRProviderType = "aliyun"
)

// OCRProviderFactory OCR提供者工厂
type OCRProviderFactory struct {
	configService ConfigServiceInterface
	organDB       *OrganDatabase
	app           AppInterface
}

// NewOCRProviderFactory 创建OCR提供者工厂
func NewOCRProviderFactory(configService ConfigServiceInterface, organDB *OrganDatabase, app AppInterface) *OCRProviderFactory {
	return &OCRProviderFactory{
		configService: configService,
		organDB:       organDB,
		app:           app,
	}
}

// CreateProvider 根据配置创建合适的OCR提供者
func (f *OCRProviderFactory) CreateProvider() (OCRProvider, error) {
	config := f.configService.GetConfig()

	// 按优先级检查配置的OCR服务
	// 1. 优先使用火山引擎OCR
	if config.APIKeys.VolcEngineOCR.APIURL != "" {
		provider := NewVolcEngineOCRProvider(&config.APIKeys.VolcEngineOCR, f.organDB)
		if err := provider.ValidateConfig(); err != nil {
			fmt.Printf("[OCR工厂] 火山引擎OCR配置验证失败: %v，尝试其他提供者\n", err)
		} else {
			fmt.Printf("[OCR工厂] 使用火山引擎OCR提供者（如果认证失败将自动回退到百度OCR）\n")
			return provider, nil
		}
	}

	// 2. 回退到百度OCR
	if config.APIKeys.OCR.APIURL != "" {
		provider := NewBaiduOCRProvider(&config.APIKeys.OCR, f.organDB, f.configService, f.app)
		if err := provider.ValidateConfig(); err != nil {
			fmt.Printf("[OCR工厂] 百度OCR配置验证失败: %v\n", err)
			return nil, fmt.Errorf("百度OCR配置无效: %w", err)
		}
		fmt.Printf("[OCR工厂] 使用百度OCR提供者\n")
		return provider, nil
	}

	// 3. 未来可以添加其他OCR提供者
	// if config.APIKeys.TencentOCR.APIURL != "" {
	//     return NewTencentOCRProvider(&config.APIKeys.TencentOCR, f.organDB), nil
	// }

	return nil, fmt.Errorf("未找到有效的OCR服务配置")
}

// GetAvailableProviders 获取所有可用的OCR提供者类型
func (f *OCRProviderFactory) GetAvailableProviders() []OCRProviderType {
	config := f.configService.GetConfig()
	available := make([]OCRProviderType, 0)

	if config.APIKeys.VolcEngineOCR.APIURL != "" {
		available = append(available, ProviderVolcEngine)
	}

	if config.APIKeys.OCR.APIURL != "" {
		available = append(available, ProviderBaidu)
	}

	return available
}

// CreateSpecificProvider 创建指定类型的OCR提供者
func (f *OCRProviderFactory) CreateSpecificProvider(providerType OCRProviderType) (OCRProvider, error) {
	config := f.configService.GetConfig()

	switch providerType {
	case ProviderVolcEngine:
		if config.APIKeys.VolcEngineOCR.APIURL == "" {
			return nil, fmt.Errorf("火山引擎OCR未配置")
		}
		return NewVolcEngineOCRProvider(&config.APIKeys.VolcEngineOCR, f.organDB), nil

	case ProviderBaidu:
		if config.APIKeys.OCR.APIURL == "" {
			return nil, fmt.Errorf("百度OCR未配置")
		}
		return NewBaiduOCRProvider(&config.APIKeys.OCR, f.organDB, f.configService, f.app), nil

	default:
		return nil, fmt.Errorf("不支持的OCR提供者类型: %s", providerType)
	}
}
