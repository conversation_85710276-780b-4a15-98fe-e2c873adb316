<template>
  <div id="app" class="compact">
    <!-- Toast通知组件 -->
    <ToastNotification 
      ref="toastNotification" 
      :config="toastConfig"
      @notification-shown="handleNotificationShown"
      @notification-removed="handleNotificationRemoved"
      @all-notifications-cleared="handleAllNotificationsCleared"
    />
    <!-- 应用头部 -->
    <div class="compact-header">
      <div class="title-bar">
        <span class="app-title">AI磁感评估</span>
        <div class="window-controls">
          <!-- 展开按钮暂时注释掉，近期不开发
          <button @click="toggleWindowSize" class="expand-btn" title="展开 (F11)">
            <svg width="16" height="16" viewBox="0 0 16 16">
              <path d="M2 2v5h2V4h3V2H2zM9 2v2h3v3h2V2H9zM4 9H2v5h5v-2H4V9zM14 9h-2v3H9v2h5V9z" fill="currentColor"/>
            </svg>
          </button>
          -->
          <button @click="minimizeWindow" class="minimize-btn" title="最小化 (F9)">
            <svg width="16" height="16" viewBox="0 0 16 16">
              <path d="M3 8h10v1H3V8z" fill="currentColor"/>
            </svg>
          </button>
        </div>
      </div>
      
      <!-- 紧凑模式状态栏 -->
      <div class="compact-status">
        <!-- 左侧二维码区域 -->
        <div class="qrcode-column">
          <div class="registration-qrcode-container">
            <img v-if="registrationQRCodeUrl" :src="registrationQRCodeUrl" alt="报到二维码" class="registration-qrcode-image" />
            <p v-else>正在生成报到二维码...</p>
          </div>
        </div>
        
        <!-- 右侧状态信息区域 -->
        <div class="status-column">
          <div class="status-item">
            <span class="label">检测站点:</span>
            <span class="value">{{ config?.SiteInfo?.SiteName || '加载中...' }}</span>
          </div>
          <div class="status-item">
            <span class="label">站点编号:</span>
            <span class="value">{{ config?.SiteInfo?.SiteID || '加载中...' }}</span>
          </div>
          <div class="status-item">
            <span class="label">设备编号:</span>
            <span class="value">{{ config?.device_info?.device_no || '未设置' }}</span>
          </div>
          <div class="status-item">
            <span class="label">今日报到:</span>
            <span class="value">{{ todayPatientCount }}人</span>
          </div>
          <div class="status-item">
            <span class="label">今日检测:</span>
            <span class="value">{{ currentRegistrationNumber }}人</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 应用主要内容 -->
    <div class="compact-content">

      <!-- 当前操作提示区 -->
      <div class="operation-status">
        <div class="progress-bar" :style="{ width: currentProgress + '%', backgroundColor: currentMode === 'B' ? '#2196f3' : '#4caf50' }"></div>
        <span class="operation-text">{{ currentOperationStatus }}</span>
        <!-- 操作按钮 -->
        <div class="operation-buttons">
          <button @click="captureScreenshotByBtn" class="operation-btn capture-btn" :disabled="captureButtonDisabled">模式<br>分析</button>
          <button @click="submitUserScreenshotTaskToAI" class="operation-btn submit-btn" v-html="submitButtonText"></button>
        </div>
      </div>
      


      <!-- 候检者管理选项卡 -->
      <div class="patient-tabs-container">
        <!-- Tab 导航 -->
        <div class="tab-navigation">
          <button 
            class="tab-button" 
            :class="{ 'active': activeTab === 'pending' }"
            @click="switchTab('pending')">
            <span class="tab-icon">⏳</span>
            <span class="tab-text">待检测 ({{ pendingRegistrations.length }})</span>
          </button>
          <button 
            class="tab-button" 
            :class="{ 'active': activeTab === 'completed' }"
            @click="switchTab('completed')">
            <span class="tab-icon">✅</span>
            <span class="tab-text">已完成 ({{ completedPatients.length }})</span>
          </button>
          <button 
            class="tab-button" 
            :class="{ 'active': activeTab === 'unanalyzed' }"
            @click="switchTab('unanalyzed')">
            <span class="tab-icon">📊</span>
            <span class="tab-text">待分析 ({{ unanalyzedPatients.length }})</span>
          </button>
          <button @click="refreshAllLists" class="refresh-btn" :disabled="isRefreshing">🔄</button>
        </div>

        <!-- Tab 内容 -->
        <div class="tab-content">
          <!-- 待检测列表 -->
          <div v-show="activeTab === 'pending'" class="tab-panel">
            <div class="patient-list">
              <div v-for="(registration, index) in displayedPendingRegistrations" :key="'pending-' + index" 
                   class="patient-item" 
                   :class="{ 'selected': selectedPatientIndex === index }"
                   @click="selectPatient(index)">
                <input type="radio" 
                       :name="'patient-radio'" 
                       :value="index" 
                       v-model="selectedPatientIndex" 
                       class="patient-radio">
                <span class="patient-name">{{ registration.name }}</span>
                <span class="patient-age">{{ calculateAge(registration.userInfo && registration.userInfo[0] ? registration.userInfo[0].birthday : '') }}</span>
                <span class="patient-gender">{{ formatGender(registration.userInfo && registration.userInfo[0] ? registration.userInfo[0].gender : 0) }}</span>
                <span class="patient-number">{{ registration.number }}</span>
                <span class="patient-time">{{ formatTime(registration.register_time) }}</span>
                <span class="patient-status pending">待检测</span>
              </div>
              <div v-if="pendingRegistrations.length > pendingDisplayLimit" class="more-patients" @click="loadMorePendingRegistrations">
                点击加载更多 (还有 {{ pendingRegistrations.length - pendingDisplayLimit }} 位候检者)
              </div>
              <div v-if="pendingRegistrations.length === 0" class="empty-state">
                <span class="empty-icon">📋</span>
                <span class="empty-text">暂无待检测的候检者</span>
              </div>
            </div>
          </div>

          <!-- 已完成列表 -->
          <div v-show="activeTab === 'completed'" class="tab-panel">
            <div class="patient-list">
              <div v-for="(patient, index) in displayedCompletedPatients" :key="'completed-' + index" 
                   class="patient-item completed">
                <span class="patient-name">{{ patient.name }}</span>
                <span class="patient-age">{{ calculateAge(patient.userInfo && patient.userInfo[0] ? patient.userInfo[0].birthday : '') }}</span>
                <span class="patient-gender">{{ formatGender(patient.userInfo && patient.userInfo[0] ? patient.userInfo[0].gender : 0) }}</span>
                <span class="patient-number">{{ patient.number }}</span>
                <span class="patient-time">{{ formatTime(patient.register_time) }}</span>
                <span class="patient-status completed">已完成</span>
                <span class="completion-time">{{ formatCompletionTime(patient.completion_time) }}</span>
              </div>
              <div v-if="completedPatients.length > completedDisplayLimit" class="more-patients" @click="loadMoreCompletedPatients">
                点击加载更多 (还有 {{ completedPatients.length - completedDisplayLimit }} 位已完成)
              </div>
              <div v-if="completedPatients.length === 0" class="empty-state">
                <span class="empty-icon">🎉</span>
                <span class="empty-text">今日暂无已完成检测的患者</span>
              </div>
            </div>
          </div>

          <!-- 未分析列表 -->
          <div v-show="activeTab === 'unanalyzed'" class="tab-panel">
            <div class="patient-list">
              <div v-for="(patient, index) in displayedUnanalyzedPatients" :key="'unanalyzed-' + index" 
                   class="patient-item unanalyzed">
                <span class="patient-name">{{ patient.name }}</span>
                <span class="patient-age">{{ calculateAge(patient.userInfo && patient.userInfo[0] ? patient.userInfo[0].birthday : '') }}</span>
                <span class="patient-gender">{{ formatGender(patient.userInfo && patient.userInfo[0] ? patient.userInfo[0].gender : 0) }}</span>
                <span class="patient-number">{{ patient.number }}</span>
                <span class="patient-time">{{ formatTime(patient.register_time) }}</span>
                <span class="patient-status unanalyzed">待分析</span>
                <span class="completion-time">{{ formatCompletionTime(patient.completion_time) }}</span>
              </div>
              <div v-if="unanalyzedPatients.length > unanalyzedDisplayLimit" class="more-patients" @click="loadMoreUnanalyzedPatients">
                点击加载更多 (还有 {{ unanalyzedPatients.length - unanalyzedDisplayLimit }} 位待分析)
              </div>
              <div v-if="unanalyzedPatients.length === 0" class="empty-state">
                <span class="empty-icon">📊</span>
                <span class="empty-text">今日暂无待分析的患者</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最新二维码显示 -->
      <div class="compact-qrcode" v-if="latestQRCode">
        <div class="section-header">
          <span>当前二维码</span>
        </div>
        <div class="qrcode-display">
          <img :src="latestQRCode" alt="患者二维码" />
        </div>
      </div>
    </div>

    <!-- 底部任务管理器状态按键 -->
    <div class="bottom-toolbar">
      <button @click="toggleTaskManagerStatus" class="task-manager-btn" :class="{ 'active': showTaskManagerStatus }">
        <svg class="task-icon" width="16" height="16" viewBox="0 0 16 16">
          <path d="M2 3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3zm1 0v10h10V3H3z" fill="currentColor"/>
          <path d="M4 5h8v1H4V5zm0 2h8v1H4V7zm0 2h6v1H4V9z" fill="currentColor"/>
        </svg>
        <span class="btn-text">任务管理器状态</span>
        <div v-if="taskStats.active_count > 0" class="task-badge">{{ taskStats.active_count }}</div>
      </button>
    </div>

    <!-- 任务管理器状态弹窗 -->
    <TaskManagerStatus
        :isVisible="showTaskManagerStatus"
        @close="handleTaskManagerClose"
      />

    <!-- 添加患者对话框 -->
    <div v-if="showAddPatientDialog" class="dialog-overlay" @click="showAddPatientDialog = false">
      <div class="dialog" @click.stop>
        <h3>添加患者</h3>
        <form @submit.prevent="handleAddPatient">
          <input v-model="newPatient.name" placeholder="患者姓名" required />
          <input v-model="newPatient.registrationNumber" placeholder="挂号号码" required />
          <div class="dialog-buttons">
            <button type="button" @click="showAddPatientDialog = false">取消</button>
            <button type="submit">添加</button>
          </div>
        </form>
      </div>
    </div>

    <!-- 加载遮罩 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>{{ loadingMessage }}</p>
    </div>

    <!-- 通知消息 -->
    <div v-if="notification.show" :class="['notification', notification.type]">
      {{ notification.message }}
    </div>
    
    <!-- 处理中对话框 -->
    <div v-if="processingDialog.show" class="processing-overlay">
      <div class="processing-dialog">
        <div class="processing-header">
          <div class="processing-icon">
            <svg class="processing-spinner" width="24" height="24" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"></animate>
                <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"></animate>
              </circle>
            </svg>
          </div>
          <h3 class="processing-title">正在处理器官数据</h3>
        </div>
        
        <div class="processing-content">
          <div class="organ-info">
            <span class="organ-label">检测器官:</span>
            <span class="organ-name">{{ processingDialog.organName }}</span>
          </div>
          
          <div class="progress-section">
            <div class="progress-info">
              <span class="step-text">步骤 {{ processingDialog.currentStep }} / {{ processingDialog.totalSteps }}</span>
              <span class="progress-percent">{{ processingDialog.progress }}%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: processingDialog.progress + '%' }"></div>
            </div>
          </div>
          
          <div class="processing-status">
            <div class="status-dots">
              <span class="dot"></span>
              <span class="dot"></span>
              <span class="dot"></span>
            </div>
            <span class="status-text">{{ processingDialog.message }}</span>
          </div>
        </div>
        
        <div class="processing-footer">
          <button @click="hideProcessingDialog" class="cancel-btn">
            <svg width="16" height="16" viewBox="0 0 16 16">
              <path d="M8 16A8 8 0 1 1 8 0a8 8 0 0 1 0 16zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z" fill="currentColor"/>
            </svg>
            取消处理
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { GetConfig, UpdateSiteInfo, UpdateCropSettings, ProcessScreenshotWorkflow,
         GenerateQRCode, AddPatient, GetPatientList, RemovePatient, ClearPatientList,
         GetCurrentRegistrationNumber, GetTodayPatientCount, GetModeConfig,
         ToggleWindowSize, SetWindowPosition, GetWindowState, MinimizeWindow,
         HandleKeyboardShortcut, GenerateRegistrationQRCode, GetRegistrations, GetSiteInfo,
         SetCurrentPatientIndex, SetCurrentPatientName, GetCurrentPatientIndex,
         MoveToNextPatient, ClearCurrentPatient, SetCompactWindow, SetExpandedWindow, SetAlwaysOnTop,
        GetPendingRegistrations, GetCompletedPatients, GetCompletedPatientsByDate,
        GetPendingPatients, GetUnanalyzedPatients, TakeConcurrentScreenshot } from '../wailsjs/go/main/App'

import { usePatientStore } from './stores/patient'
import { useDeviceStore } from './stores/device'
import { useNotificationStore } from './stores/notification'

import ToastNotification from './components/ToastNotification.vue'
import ErrorBoundary from './components/ErrorBoundary.vue'
import TaskManagerStatus from './components/TaskManagerStatus.vue'
import { TOAST_CONFIG, getToastConfig, TOAST_TEMPLATES } from './config/toastConfig.js'
import toastManager from './utils/toastManager.js'
import eventManager from './utils/eventManager.js'
import notificationManager from './utils/notificationManager.js'
import errorHandler from './utils/errorHandler.js'
import errorMonitor from './utils/errorMonitor.js'
import { 
  withErrorHandling, 
  safeAsync, 
  withScreenshotErrorHandling, 
  withOCRErrorHandling, 
  withNetworkErrorHandling 
} from './utils/asyncErrorHandler.js'

export default {
  name: 'App',
  components: {
    ToastNotification,
    ErrorBoundary,
    TaskManagerStatus
  },
  setup() {
    const patientStore = usePatientStore()
    const deviceStore = useDeviceStore()
    const notificationStore = useNotificationStore()
    
    return {
      patientStore,
      deviceStore,
      notificationStore
    }
  },
  data() {
    return {
      // Toast通知配置
      toastConfig: {
        ...TOAST_CONFIG,
        debug: true, // 启用调试模式
        maxToasts: 3, // 减少最大显示数量以适应紧凑界面
      },
      logs: [],
      currentTime: '',
      loading: false,
      loadingMessage: '',
      // 缓存管理相关
      lastCacheCleanDate: null, // 上次缓存清理日期
      dailyCacheTimer: null, // 日期检查定时器
      currentDate: new Date().toDateString(), // 当前日期字符串
      // 处理中对话框状态
      processingDialog: {
        show: false,
        organName: '',
        currentStep: 0,
        totalSteps: 10,
        progress: 0,
        startTime: null,
        message: '正在分析数据，请稍候...'
      },
      timeInterval: null,
      // 新增字段
      latestQRCode: null,
      registrationQRCodeUrl: '', // 用于存储报到二维码URL
      showAddPatientDialog: false,
      newPatient: {
        name: '',
        registrationNumber: ''
      },

      // 当前操作状态提示
      currentOperationStatus: '等待您选择目标器官/部位，进行B02生化平衡分析采样...',
      // 进度条相关
      currentProgress: 0, // 当前进度百分比
      currentMode: '', // 当前模式 B 或 C


      // Tab选项卡相关
      activeTab: 'pending', // 默认激活待检测tab
      pendingRegistrations: [], // 待检测患者列表
      completedPatients: [], // 已完成患者列表
      unanalyzedPatients: [], // 已检测未分析患者列表
      pendingDisplayLimit: 10, // 待检测列表显示限制
      completedDisplayLimit: 10, // 已完成列表显示限制
      unanalyzedDisplayLimit: 10, // 未分析列表显示限制
      isRefreshing: false, // 刷新状态
      // 按钮状态管理
      submitButtonState: 'extract', // 'extract' 或 'generate'


      captureButtonDisabled: true, // 控制"模式分析"按钮的禁用状态
      
      // 任务管理器状态相关
      showTaskManagerStatus: false, // 控制任务管理器状态弹窗显示（默认隐藏）
      taskStats: {
        active_count: 0,
        queue_size: 0,
        total_submitted: 0,
        completed_tasks: 0,
        failed_tasks: 0,
         cancelled_tasks: 0
       },
       taskStatsTimer: null // 任务统计更新定时器
    }
  },
  computed: {

    todayPatientCount() {
      return this.patientStore.todayPatientCount
    },
    currentRegistrationNumber() {
      return this.patientStore.currentRegistrationNumber
    },
    displayedRegistrations() {
      return this.patientStore.displayedRegistrations
    },
    currentPatient() {
      return this.patientStore.currentPatient
    },
    // 从 stores 获取状态
    config() {
      return this.deviceStore.config
    },
    patients() {
      return this.patientStore.patients
    },
    registrations() {
      return this.patientStore.registrations
    },

    notification() {
      return this.notificationStore.currentNotification
    },


    // Tab相关计算属性
    displayedPendingRegistrations() {
      return this.pendingRegistrations.slice(0, this.pendingDisplayLimit)
    },
    displayedCompletedPatients() {
      return this.completedPatients.slice(0, this.completedDisplayLimit)
    },
    displayedUnanalyzedPatients() {
      return this.unanalyzedPatients.slice(0, this.unanalyzedDisplayLimit)
    },
    selectedPatientIndex: {
      get() {
        return this.patientStore.selectedPatientIndex
      },
      set(value) {
        this.patientStore.setSelectedPatientIndex(value)
      }
    },
    // 动态按钮文本
    submitButtonText() {
      return this.submitButtonState === 'extract' ? '开始提取<br>B02/C03' : '提交生成<br>健康报告'
    },
    

  },
  async mounted() {
    // 初始化错误监控
    this.initializeErrorHandling()
    
    await this.initializeApp()
    
    this.startTimeUpdate()
    this.setupKeyboardShortcuts()

    // 设置Wails通知事件监听器
    this.setupWailsNotificationListeners()
    
    // 初始化Toast管理器
    this.initializeToastManager()
    
    // 初始化事件管理器和通知管理器
    this.initializeEventAndNotificationManagers()
    
    // 初始化缓存清理机制
    this.initializeCacheManagement()

    await this.generateRegistrationQRCode() // 生成报到二维码
    
    // 启动任务统计更新
    this.startTaskStatsUpdate()
    await this.patientStore.loadRegistrations() // 加载候检者列表

    // 初始化加载待检测和已完成患者列表
    await this.loadPendingRegistrations()
    await this.loadCompletedPatients()



    // 执行初始状态检查
    this.checkAndUpdateCurrentPatientStatus()
  },
  beforeUnmount() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
    // 停止缓存管理定时器
    if (this.dailyCacheTimer) {
      clearInterval(this.dailyCacheTimer)
    }
    // 停止患者轮询
    this.patientStore.stopPolling()
    // 停止设备配置监听器
    this.deviceStore.stopConfigWatcher()
    // 停止任务统计更新
    this.stopTaskStatsUpdate()
  },
  methods: {
    // 初始化错误处理
    initializeErrorHandling() {
      try {
        // 初始化错误监控
        errorMonitor.init({
          reportEndpoint: null, // 开发环境不上报
          enableConsoleCapture: true,
          enableUnhandledRejection: true,
          enableResourceError: true,
          enablePerformanceMonitoring: true,
          environment: process.env.NODE_ENV || 'development'
        })
        
        // 设置用户ID（如果有的话）
        if (this.config?.device_info?.device_no) {
          errorMonitor.setUserId(this.config.device_info.device_no)
        }
        
        // 监听错误事件
        this.setupErrorEventListeners()
        
        console.log('错误处理系统初始化完成')
      } catch (error) {
        console.error('错误处理系统初始化失败:', error)
      }
    },
    
    // 设置错误事件监听器
    setupErrorEventListeners() {
      // 监听全局错误事件
      window.addEventListener('error-report', (event) => {
        this.handleErrorReport(event.detail)
      })
      
      // 监听组件错误事件
      this.$on('component-error', this.handleComponentError)
    },
    
    // 处理错误报告
    handleErrorReport(errorData) {
      console.log('收到错误报告:', errorData)
      
      // 显示用户友好的错误信息
      if (errorData.showToUser && this.$refs.toastNotification) {
        this.$refs.toastNotification.showToast({
          title: '操作失败',
          message: errorData.userMessage || '操作过程中出现错误，请重试',
          type: 'error',
          duration: 5000
        })
      }
    },
    
    // 处理组件错误
    handleComponentError(errorInfo) {
      console.error('组件错误:', errorInfo)
      errorMonitor.reportError(errorInfo.error, {
        component: errorInfo.component?.$options?.name || 'Unknown',
        errorInfo: errorInfo.errorInfo
      })
    },
    
    // 安全执行异步操作
    async safeExecute(fn, context = '', options = {}) {
      return await safeAsync(fn, context, {
        showToUser: true,
        ...options
      })
    },
    
    // 设置Wails通知事件监听器
    setupWailsNotificationListeners() {
      // 注释掉重复的事件监听，避免与ToastNotification组件冲突
      // window.runtime.EventsOn('showToastNotification', (data) => {
      //   // 同步更新操作状态显示
      //   this.updateOperationStatus(data.message || data.title || '系统通知')
      // })

      // 监听进度更新事件
      window.runtime.EventsOn('wails:update-progress', (data) => {
        if (this.$refs.toastNotification) {
          this.$refs.toastNotification.updateProgress(data.id, data.progress, data.message)
        }
      })

      // 监听操作状态更新事件
      window.runtime.EventsOn('updateOperationStatus', (data) => {
        console.log('[App] 收到操作状态更新:', data)
        this.currentOperationStatus = data.status
        this.currentMode = data.mode

      })

      // 监听进度条更新事件
      window.runtime.EventsOn('updateProgress', (data) => {
        console.log('[App] 收到进度更新:', data)
        this.currentProgress = data.progress
        this.currentMode = data.mode


      })
    },
    
    // 提交用户截图任务到AI
    async submitUserScreenshotTaskToAI() {
      if (this.submitButtonState === 'extract') {
        // 第一次点击：开始提取B02/C03
        console.log('[App] 操作者开始提取B02/C03数据')
        
        // 切换按钮状态
        this.submitButtonState = 'generate'
        
        // 启用"模式分析"按钮
        this.captureButtonDisabled = false
        
        // 显示提取开始的通知
        if (this.$refs.toastNotification) {
          this.$refs.toastNotification.showToast({
            title: '开始数据提取分析',
            message: '请开始对检测结果分析中的B02/C03选项，进行提取分析的相关操作！',
            type: 'info',
            duration: 8000,
            showProgress: false
          })
        }
        
      } else {
        // 第二次点击：提交生成健康报告
        console.log('[App] 操作者提交用户截图任务到AI，以生成健康分析报告')
        
        // 重置按钮状态
        this.submitButtonState = 'extract'
        
        // 禁用"模式分析"按钮
        this.captureButtonDisabled = true
        
        // 需要在这里进行扣子API调用处理
        if (this.$refs.toastNotification) {
          const result = this.$refs.toastNotification.showToast({
            title: '提交检测数据',
            message: '开始启动AI大模型分析生成健康报告，您可以开始下一位受检者检测工作...',
            type: 'success',
            duration: 8000,
            showProgress: false
          })
          console.log('[App] showToast返回结果:', result)
        } else {
          console.error('[App] 未找到ToastNotification组件引用')
        }
      }
    },
     
     // 抓取用户截图数据
     async captureScreenshotData() {
       console.log('[App] 抓取用户截图数据')
       try {
         // 调用后端的ShowSuccessNotification方法
         const { ShowSuccessNotification } = await import('../wailsjs/go/main/App.js')
         console.log('[App] 调用后端ShowSuccessNotification方法')
         await ShowSuccessNotification('测试后端通知', '这是来自后端的测试通知，用于验证事件传递', 5000)
         console.log('[App] 后端方法调用完成')
       } catch (error) {
         console.error('[App] 调用后端通知方法失败:', error)
       }
     },
     
     // 按钮触发的智能截图功能
     async captureScreenshotByBtn() {
       console.log('[App] 开始按钮触发的智能截图')
       
       try {
         // 显示加载状态
         this.showLoading('正在进行检测结果获取和初步分析...')
         
         // 导入后端方法
         const { CaptureScreenshotByBtn } = await import('../wailsjs/go/main/App.js')
         
         // 调用后端智能截图方法
         console.log('[App] 调用后端CaptureScreenshotByBtn方法')
         const result = await CaptureScreenshotByBtn()
         
         // 隐藏加载状态
         this.hideLoading()
         
         if (result && result.success) {
           // 显示成功通知
           this.showNotification(
             `检测结果获取和初步分析完成！检测到${result.detected_mode}模式\n器官: ${result.organ_name}`,
             'success'
           )
           
           // 重置按钮状态为开始提取
           this.submitButtonState = 'extract'
           
           // 添加日志记录
           this.addLog({
             time: new Date().toLocaleString(),
             action: '智能截图',
             mode: result.detected_mode,
             result: '成功',
             message: `器官: ${result.organ_name}, 置信度: ${(result.confidence * 100).toFixed(1)}%`,
             taskId: result.task_id
           })
           
           console.log('[App] 智能截图成功:', {
             taskId: result.task_id,
             detectedMode: result.detected_mode,
             organName: result.organ_name,
             confidence: result.confidence,
             imagePath: result.image_path
           })
         } else {
           // 显示错误通知
           this.showNotification('检测结果获取和初步分析失败，请重试', 'error')
           console.error('[App] 智能截图失败:', result)
         }
         
       } catch (error) {
         // 隐藏加载状态
         this.hideLoading()
         
         // 显示错误通知
         this.showNotification(`检测结果获取和初步分析失败: ${error.message || error}`, 'error')
         
         // 添加错误日志
         this.addLog({
           time: new Date().toLocaleString(),
           action: '智能截图',
           mode: '未知',
           result: '失败',
           message: error.message || error.toString()
         })
         
         console.error('[App] 智能截图异常:', error)
       }
     },
     
     formatGender(gender) {
      console.log('formatGender - gender:', gender, 'type:', typeof gender);
      if (gender === 1) {
        return '男';
      } else if (gender === 2) {
        return '女';
      } else {
        return '未知';
      }
    },
    calculateAge(birthDate) {
      if (!birthDate) return '';
      const birth = new Date(birthDate);
      const today = new Date();
      let age = today.getFullYear() - birth.getFullYear();
      const m = today.getMonth() - birth.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < birth.getDate())) {
        age--;
      }
      return age + '岁';
    },
    async initializeApp() {
      try {
        this.notificationStore.showLoading('正在初始化应用...')
        
        // 加载设备配置
        const configResult = await this.deviceStore.loadConfig()
        console.log('initializeApp - config loaded:', configResult)
        
        // 获取站点信息
        const siteResult = await this.deviceStore.fetchSiteInfo()
        if (siteResult.success) {
          this.notificationStore.showSuccess(siteResult.message)
        } else {
          this.notificationStore.showError(siteResult.message)
        }

        // 保留事件监听器作为备用机制
        window.runtime.EventsOn('siteInfoUpdated', (newSiteInfo) => {
          console.log('siteInfoUpdated - newSiteInfo:', JSON.stringify(newSiteInfo, null, 2))
          const updateResult = this.deviceStore.updateSiteInfo(newSiteInfo)
          if (updateResult.success) {
            this.notificationStore.showSuccess(updateResult.message)
          } else {
            this.notificationStore.showError(updateResult.message)
          }
        });
        
        // 监听处理中通知事件
        window.runtime.EventsOn('showProcessingNotification', (data) => {
          console.log('showProcessingNotification - data:', data);
          this.showProcessingDialog(data.organName, data.currentStep, data.totalSteps, data.message);
        });
        
        // 监听处理进度更新事件
        window.runtime.EventsOn('updateProcessingProgress', (data) => {
          console.log('updateProcessingProgress - data:', data);
          this.updateProcessingProgress(data.currentStep, data.totalSteps, data.progress);
        });
        
        // 监听隐藏处理对话框事件
        window.runtime.EventsOn('hideProcessingNotification', () => {
          console.log('hideProcessingNotification');
          this.hideProcessingDialog();
        });
        
        // 加载模式配置
        const modeConfig = await GetModeConfig()
        console.log('Mode config loaded:', modeConfig)
        
        // 加载候检者列表
        await this.patientStore.loadRegistrations()
        
        // 加载所有患者分类列表
        await Promise.all([
          this.loadPendingRegistrations(),
          this.loadCompletedPatients(),
          this.loadUnanalyzedPatients()
        ])
        
        // 启动配置监听器，定期检查配置更新
        this.deviceStore.startConfigWatcher()
        
        this.notificationStore.hideLoading()
        this.notificationStore.showSuccess('应用初始化成功')
      } catch (error) {
        this.notificationStore.hideLoading()
        this.notificationStore.showError(`初始化失败: ${error}`)
        console.error('初始化应用失败:', error)
      }
    },

    async generateRegistrationQRCode() {
      try {
        const result = await GenerateRegistrationQRCode(); // 调用后端方法
        if (result && result.qr_code_base64) {
          this.registrationQRCodeUrl = `data:image/png;base64,${result.qr_code_base64}`;
        } else if (result && result.file_path) {
          // Handle file_path if necessary, e.g., if Wails AssetServer serves it
          console.log('报到二维码文件路径:', result.file_path);
          // this.registrationQRCodeUrl = result.file_path; // This might require specific Wails setup
          this.notificationStore.showInfo('报到二维码已生成 (路径方式，可能无法直接显示)');
        } else {
          console.error('生成报到二维码失败或返回数据格式不正确', result);
          this.notificationStore.showError('生成报到二维码失败');
        }
      } catch (error) {
        console.error('调用生成报到二维码方法失败:', error);
        this.notificationStore.showError(`生成报到二维码失败: ${error.message || error}`);
      }
    },
    
    async updateCropSettings(cropSettings) {
      const result = await this.deviceStore.updateCropSettings(cropSettings)
      if (result.success) {
        this.notificationStore.showSuccess(result.message)
      } else {
        this.notificationStore.showError(result.message)
      }
    },
    
    async updateNotificationMode() {
      const result = await this.deviceStore.updateNotificationMode()
      if (result.success) {
        this.notificationStore.showSuccess(result.message)
      } else {
        this.notificationStore.showError(result.message)
      }
    },
    
    async handleScreenshot(mode) {
      try {
        // 对于B和C模式，使用并发截图服务
        if (mode === 'B' || mode === 'C' || mode === '生化平衡分析' || mode === '病理形态学分析') {
          this.showLoading(`正在处理[${mode}]截图...`)
          
          // 立即显示截图开始的Toast通知
          this.showNotification(`开始处理${mode}模式截图`, 'info')
          
          try {
            // 使用并发截图服务
            const result = await TakeConcurrentScreenshot(mode) // 简化截图调用，不再使用轮次
            
            this.hideLoading()
            this.showNotification(`${mode}模式截图处理完成`, 'success')
            
            this.addLog({
              time: new Date().toLocaleString(),
              action: '并发截图',
              mode: mode,
              result: '成功',
              message: result.message || '截图任务已提交'
            })
          } catch (concurrentError) {
            console.warn('并发截图失败，回退到传统方式:', concurrentError)
            
            // 回退到传统截图方式
            const imageUrl = await ProcessScreenshotWorkflow(mode)
            
            this.hideLoading()
            this.showNotification(`${mode}模式数据处理完成: ${imageUrl}`, 'success')
            
            this.addLog({
              time: new Date().toLocaleString(),
              action: '传统截图（回退）',
              mode: mode,
              result: '成功',
              url: imageUrl
            })
          }
        } else {
          // 对于A模式或其他模式，继续使用传统方式
          this.showLoading(`正在处理和分析[${mode}]数据...`)
          
          const imageUrl = await ProcessScreenshotWorkflow(mode)
          
          this.hideLoading()
          this.showNotification(`数据处理完成: ${imageUrl}`, 'success')
          
          this.addLog({
            time: new Date().toLocaleString(),
            action: '数据处理',
            mode: mode,
            result: '成功',
            url: imageUrl
          })
        }
      } catch (error) {
        this.hideLoading()
        this.showNotification(`数据处理失败: ${error}`, 'error')
        
        this.addLog({
          time: new Date().toLocaleString(),
          action: '截图处理',
          mode: mode,
          result: '失败',
          error: error.toString()
        })
      }
    },
    
    // async uploadLatestScreenshot() {
    //   // 已注释：功能已由A、B、C快捷键的ProcessScreenshotWorkflow替代
    //   try {
    //     this.showLoading('正在上传最新截图...')
        
    //     // 调用后端方法上传最新截图
    //     const result = await UploadLatestScreenshot()
        
    //     this.hideLoading()
    //     this.showNotification(`上传完成: ${result}`, 'success')
        
    //     this.addLog({
    //       time: new Date().toLocaleString(),
    //       action: '快捷键上传',
    //       user: '医生或健康专家',
    //       result: '成功',
    //       url: result
    //     })
    //   } catch (error) {
    //     this.hideLoading()
    //     this.showNotification(`上传失败: ${error}`, 'error')
        
    //     this.addLog({
    //       time: new Date().toLocaleString(),
    //       action: '快捷键上传',
    //       user: '医生或健康专家',
    //       result: '失败',
    //       error: error.toString()
    //     })
    //   }
    // },
    
    async generateQRCode() {
      try {
        this.showLoading('正在生成二维码...')
        this.updateOperationStatus('正在生成二维码...')
        
        const qrCodePath = await GenerateQRCode()
        this.latestQRCode = qrCodePath // 保存最新的二维码
        
        this.hideLoading()
        this.showNotification('二维码生成成功', 'success')
        this.updateOperationStatus('二维码生成完成')
        
        return qrCodePath
      } catch (error) {
        this.hideLoading()
        this.showNotification(`生成二维码失败: ${error}`, 'error')
        throw error
      }
    },



    async handleAddPatient() {
      if (!this.newPatient.name || !this.newPatient.registrationNumber) {
        this.showNotification('请填写完整的患者信息', 'warning')
        return
      }
      
      try {
        await this.addPatient(this.newPatient.name)
        this.newPatient = { name: '', registrationNumber: '' }
        this.showAddPatientDialog = false
        this.updateActivity()
      } catch (error) {
        this.showNotification(`添加患者失败: ${error}`, 'error')
      }
    },


    
    async addPatient(name) {
      const result = await this.patientStore.addPatient(name)
      if (result.success) {
        this.notificationStore.showSuccess(result.message)
        this.addLog({
          time: new Date().toLocaleString(),
          action: '添加患者',
          user: name,
          result: '成功'
        })
      } else {
        this.notificationStore.showError(result.message)
      }
    },
    
    async removePatient(index) {
      const result = await this.patientStore.removePatient(index)
      if (result.success) {
        this.notificationStore.showSuccess(result.message)
        this.addLog({
          time: new Date().toLocaleString(),
          action: '移除患者',
          user: result.patientName || '未知',
          result: '成功'
        })
      } else {
        this.notificationStore.showError(result.message)
      }
    },
    
    async clearPatients() {
      const result = await this.patientStore.clearPatients()
      if (result.success) {
        this.notificationStore.showSuccess(result.message)
        this.addLog({
          time: new Date().toLocaleString(),
          action: '清空患者列表',
          user: '系统',
          result: '成功'
        })
      } else {
        this.notificationStore.showError(result.message)
      }
    },

    // Tab切换相关方法
    switchTab(tabName) {
      this.activeTab = tabName
      if (tabName === 'completed') {
        this.loadCompletedPatients()
      } else if (tabName === 'pending') {
        this.loadPendingRegistrations()
      } else if (tabName === 'unanalyzed') {
        this.loadUnanalyzedPatients()
      }
    },

    // 加载待检测患者列表
    async loadPendingRegistrations() {
      try {
        const result = await GetPendingRegistrations()
        this.pendingRegistrations = result || []
        
        // 按报到时间正序排列（先报到的在前面）
        this.pendingRegistrations.sort((a, b) => new Date(a.register_time) - new Date(b.register_time))
        
        console.log('待检测患者列表:', this.pendingRegistrations)
      } catch (error) {
        console.error('加载待检测患者列表失败:', error)
        this.pendingRegistrations = []
      }
    },

    // 加载已完成患者列表
    async loadCompletedPatients(date = null) {
      try {
        let result
        if (date) {
          // 使用指定日期获取已完成患者列表
          result = await GetCompletedPatientsByDate(date)
        } else {
          // 使用今天日期获取已完成患者列表
          const today = new Date().toISOString().split('T')[0] // 格式: YYYY-MM-DD
          result = await GetCompletedPatientsByDate(today)
        }
        this.completedPatients = result || []
        console.log('已完成患者列表（已检测）:', this.completedPatients)
      } catch (error) {
        console.error('加载已完成患者列表失败:', error)
        this.completedPatients = []
      }
    },

    // 加载未分析患者列表
    async loadUnanalyzedPatients(date = null) {
      try {
        const targetDate = date || new Date().toISOString().split('T')[0] // 格式: YYYY-MM-DD
        const result = await GetUnanalyzedPatients(targetDate)
        this.unanalyzedPatients = result || []
        console.log('未分析患者列表（已检测未分析）:', this.unanalyzedPatients)
        return this.unanalyzedPatients
      } catch (error) {
        console.error('加载未分析患者列表失败:', error)
        this.unanalyzedPatients = []
        return []
      }
    },

    // 刷新所有列表
    async refreshAllLists() {
      this.isRefreshing = true
      try {
        await Promise.all([
          this.loadPendingRegistrations(),
          this.loadCompletedPatients(),
          this.loadUnanalyzedPatients(),
          this.patientStore.loadRegistrations()
        ])
        this.notificationStore.showSuccess('列表刷新成功')
        
        // 刷新后检查当前患者状态
        this.checkAndUpdateCurrentPatientStatus()
      } catch (error) {
        console.error('刷新列表失败:', error)
        this.notificationStore.showError('刷新列表失败')
      } finally {
        this.isRefreshing = false
      }
    },

    // 加载更多待检测患者
    loadMorePendingRegistrations() {
      this.pendingDisplayLimit += 10
    },

    // 加载更多已完成患者
    loadMoreCompletedPatients() {
      this.completedDisplayLimit += 10
    },

    // 加载更多未分析患者
    loadMoreUnanalyzedPatients() {
      this.unanalyzedDisplayLimit += 10
    },

    // 选择患者
    selectPatient(index) {
      this.selectedPatientIndex = index
    },

    // 格式化完成时间
    formatCompletionTime(completionTime) {
      if (!completionTime) return ''
      const date = new Date(completionTime)
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 格式化时间
     formatTime(timeStr) {
       if (!timeStr) return ''
       const date = new Date(timeStr)
       return date.toLocaleString('zh-CN', {
         month: '2-digit',
         day: '2-digit',
         hour: '2-digit',
         minute: '2-digit'
       })
     },
    
    handleProcessComplete(result) {
      this.showNotification(`处理完成: ${result}`, 'success')
    },
    
    addLog(log) {
      this.logs.unshift(log)
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(0, 100)
      }
    },
    

    
    showNotification(message, type = 'info') {
      // 调用notificationStore（保持原有逻辑）
      this.notificationStore.showNotification(message, type)
      
      // 同时调用ToastNotification组件显示Toast
      if (this.$refs.toastNotification) {
        this.$refs.toastNotification.showToast({
          title: this.getNotificationTitle(type),
          message: message,
          type: type,
          duration: this.getNotificationDuration(type),
          showProgress: false
        })
      }
    },
    
    // 根据通知类型获取标题
    getNotificationTitle(type) {
      const titleMap = {
        'success': '操作成功',
        'error': '操作失败',
        'warning': '警告',
        'info': '提示'
      }
      return titleMap[type] || '通知'
    },
    
    // 根据通知类型获取持续时间
    getNotificationDuration(type) {
      const durationMap = {
        'success': 3000,
        'error': 5000,
        'warning': 4000,
        'info': 3000
      }
      return durationMap[type] || 3000
    },
    
    showProcessingDialog(organName, currentStep = 1, totalSteps = 10, message = '正在分析数据，请稍候...') {
      this.processingDialog = {
        show: true,
        organName: organName || '未知器官',
        currentStep: currentStep,
        totalSteps: totalSteps,
        progress: Math.round((currentStep / totalSteps) * 100),
        startTime: new Date(),
        message: message
      };
    },
    
    hideProcessingDialog() {
      this.processingDialog.show = false;
    },
    
    updateProcessingProgress(currentStep, totalSteps, progress) {
      if (this.processingDialog.show) {
        this.processingDialog.currentStep = currentStep;
        this.processingDialog.totalSteps = totalSteps;
        this.processingDialog.progress = progress;
      }
    },
    
    startTimeUpdate() {
      this.updateTime()
      this.timeInterval = setInterval(this.updateTime, 1000)
    },
    
    updateTime() {
      this.currentTime = new Date().toLocaleString()
    },
    
    setupKeyboardShortcuts() {
      // 监听键盘事件
      document.addEventListener('keydown', (event) => {
        // F12 快捷键处理
        if (event.key === 'F12') {
          event.preventDefault()
          this.handleFunctionKey(event.key)
        }
        
        // Ctrl+Shift 组合键处理
        if (event.ctrlKey && event.shiftKey) {
          event.preventDefault()
          this.handleHotkeyCombo(event.key.toUpperCase())
        }
      })
    },
    
    async handleFunctionKey(key) {
      const keyMap = {
        'F12': () => this.clearPatients()
      };
      
      if (keyMap[key]) {
        await keyMap[key]()
        // 同时调用后端的快捷键处理
        try {
          await HandleKeyboardShortcut(key)
        } catch (error) {
          console.error('后端快捷键处理失败:', error)
        }
      }
    },
    
    async handleHotkeyCombo(key) {
      const hotkeyMap = {
        'A': () => this.handleScreenshot('器官问题来源分析'),
        'B': () => this.handleScreenshot('生化平衡分析'),
        'C': () => this.handleScreenshot('病理形态学分析'),
        // 'U': () => this.uploadLatestScreenshot() // 已注释，功能由A、B、C快捷键替代
      };
      
      if (hotkeyMap[key]) {
        await hotkeyMap[key]()
        // 同时调用后端的快捷键处理
        try {
          await HandleKeyboardShortcut(`Ctrl+Shift+${key}`)
        } catch (error) {
          console.error('后端快捷键处理失败:', error)
        }
      }
    },

    // 配置监听器方法
    startConfigWatcher() {
      // console.log('配置监听器已移除定时获取逻辑，仅在初始化时获取配置')
      // // 每30秒检查一次配置更新
      // this.configWatcherTimer = setInterval(async () => {
      //   try {
      //     const newConfig = await GetConfig()
      //     // 检查站点信息是否有变化
      //     if (this.hasConfigChanged(newConfig)) {
      //       const oldSiteInfo = this.config?.SiteInfo || {}
      //       this.config = newConfig
      //       this.showNotification(`站点信息已更新: ${newConfig.SiteInfo?.SiteName || '未知站点'}`, 'success')
      //       console.log('配置已更新:', {
      //         旧站点: oldSiteInfo.SiteName || '未设置',
      //         新站点: newConfig.SiteInfo?.SiteName || '未设置'
      //       })
      //     }
      //   } catch (error) {
      //     console.error('检查配置更新失败:', error)
      //   }
      // }, 30000) // 30秒检查一次
    },

    // 检查配置是否有变化
    hasConfigChanged(newConfig) {
      if (!this.config || !newConfig) return true
      
      const oldSiteInfo = this.config.SiteInfo || {}
      const newSiteInfo = newConfig.SiteInfo || {}
      
      return (
        oldSiteInfo.SiteID !== newSiteInfo.SiteID ||
        oldSiteInfo.SiteName !== newSiteInfo.SiteName ||
        oldSiteInfo.SiteType !== newSiteInfo.SiteType ||
        oldSiteInfo.ParentOrg !== newSiteInfo.ParentOrg
      )
    },

    // 候检者相关方法
    async loadRegistrations() {
      const result = await this.patientStore.loadRegistrations()
      if (!result.success) {
        this.notificationStore.showError(result.message)
      }
    },

    async refreshRegistrations() {
      const result = await this.patientStore.refreshRegistrations()
      if (result.success) {
        this.notificationStore.showSuccess(result.message)
      } else {
        this.notificationStore.showError(result.message)
      }
    },

    // 选择候检者
    async selectPatient(index) {
      const result = await this.patientStore.selectPatient(index)
      if (result.success) {
        this.notificationStore.showInfo(result.message)
      } else {
        this.notificationStore.showError(result.message)
      }
    },



    // 条件性轮询 - 在特定条件下尝试获取新的候检者信息
    async startConditionalPolling() {
      const result = await this.patientStore.startConditionalPolling()
      if (result.success && result.newCount > 0) {
        this.notificationStore.showSuccess(`发现 ${result.newCount} 位新候检者`)
      }
    },

    loadMoreRegistrations() {
      this.patientStore.loadMoreRegistrations()
    },

    formatTime(timeStr) {
      if (!timeStr) return ''
      // 后端已经格式化为 "2006-01-02 15:04:05" 格式，直接显示月日时分
      try {
        if (timeStr.includes('-') && timeStr.includes(':')) {
          // 解析 "2006-01-02 15:04:05" 格式
          const parts = timeStr.split(' ')
          if (parts.length === 2) {
            const datePart = parts[0].split('-')
            const timePart = parts[1].split(':')
            if (datePart.length === 3 && timePart.length >= 2) {
              return `${datePart[1]}/${datePart[2]} ${timePart[0]}:${timePart[1]}`
            }
          }
        }
        return timeStr
      } catch (error) {
        return timeStr
      }
    },

    async generateRegistrationQRCode() {
      try {
        const result = await GenerateRegistrationQRCode(); // 调用后端方法
        if (result && result.qr_code_base64) {
          this.registrationQRCodeUrl = `data:image/png;base64,${result.qr_code_base64}`;
        } else if (result && result.file_path) {
          // Handle file_path if necessary, e.g., if Wails AssetServer serves it
          console.log('报到二维码文件路径:', result.file_path);
          // this.registrationQRCodeUrl = result.file_path; // This might require specific Wails setup
          this.showNotification('报到二维码已生成 (路径方式，可能无法直接显示)', 'info');
        } else {
          console.error('生成报到二维码失败或返回数据格式不正确', result);
          this.showNotification('生成报到二维码失败', 'error');
        }
      } catch (error) {
        console.error('调用生成报到二维码方法失败:', error);
        this.showNotification(`生成报到二维码失败: ${error.message || error}`, 'error');
      }
    },
    
    async updateCropSettings(cropSettings) {
      try {
        await UpdateCropSettings(cropSettings)
        this.config = await GetConfig()
        
        // 处理device_no字段，从mac_address生成去除冒号的版本
        if (this.config.device_info && this.config.device_info.mac_address) {
          this.config.device_info.device_no = this.config.device_info.mac_address.replace(/:/g, '')
        }
        
        this.showNotification('裁剪设置更新成功', 'success')
      } catch (error) {
        this.showNotification(`更新裁剪设置失败: ${error}`, 'error')
      }
    },

    // async uploadLatestScreenshot() {
    //   // 已注释：功能已由A、B、C快捷键的ProcessScreenshotAndUpload替代
    //   try {
    //     this.showLoading('正在上传最新截图...')
        
    //     // 调用后端方法上传最新截图
    //     const result = await UploadLatestScreenshot()
        
    //     this.hideLoading()
    //     this.showNotification(`上传完成: ${result}`, 'success')
        
    //     this.addLog({
    //       time: new Date().toLocaleString(),
    //       action: '快捷键上传',
    //       user: '医生或健康专家',
    //       result: '成功',
    //       url: result
    //     })
    //   } catch (error) {
    //     this.hideLoading()
    //     this.showNotification(`上传失败: ${error}`, 'error')
        
    //     this.addLog({
    //       time: new Date().toLocaleString(),
    //       action: '快捷键上传',
    //       user: '医生或健康专家',
    //       result: '失败',
    //       error: error.toString()
    //     })
    //   }
    // },
    
    async generateQRCode() {
      try {
        this.showLoading('正在生成二维码...')
        
        const qrCodePath = await GenerateQRCode()
        this.latestQRCode = qrCodePath // 保存最新的二维码
        
        this.hideLoading()
        this.showNotification('二维码生成成功', 'success')
        
        return qrCodePath
      } catch (error) {
        this.hideLoading()
        this.showNotification(`生成二维码失败: ${error}`, 'error')
        throw error
      }
    },

    // 窗体控制方法
    async toggleWindowSize() {
      try {
        // 根据当前状态切换到相应模式
        if (this.windowState.isExpanded) {
          await SetCompactWindow()
        } else {
          await SetExpandedWindow()
        }
        const state = await GetWindowState()
        this.windowState = state
        this.updateActivity()
      } catch (error) {
        this.showNotification(`切换窗体大小失败: ${error}`, 'error')
      }
    },

    async toggleWindowPosition() {
      try {
        const newPosition = this.windowState.position === 'left' ? 'right' : 'left'
        await SetWindowPosition(newPosition)
        this.windowState.position = newPosition
        this.updateActivity()
      } catch (error) {
        this.showNotification(`切换窗体位置失败: ${error}`, 'error')
      }
    },

    async minimizeWindow() {
      try {
        await MinimizeWindow()
        this.updateActivity()
      } catch (error) {
        this.showNotification(`最小化窗体失败: ${error}`, 'error')
      }
    },

    async handleQuickScreenshot() {
      try {
        this.updateOperationStatus('正在进行快速截图...')
        // 使用默认模式进行快速截图
        await this.handleScreenshot('default', '快速操作')
        this.updateOperationStatus('快速截图完成')
        this.updateActivity()
      } catch (error) {
        this.updateOperationStatus('快速截图失败')
        this.showNotification(`快速截图失败: ${error}`, 'error')
      }
    },

    async handleAddPatient() {
      if (!this.newPatient.name || !this.newPatient.registrationNumber) {
        this.showNotification('请填写完整的患者信息', 'warning')
        return
      }
      
      try {
        await this.addPatient(this.newPatient.name)
        this.newPatient = { name: '', registrationNumber: '' }
        this.showAddPatientDialog = false
        this.updateActivity()
      } catch (error) {
        this.showNotification(`添加患者失败: ${error}`, 'error')
      }
    },

    // 活动跟踪
    updateActivity() {
      this.lastActivityTime = Date.now()
      this.resetInactivityTimer()
    },

    resetInactivityTimer() {
      if (this.inactivityTimer) {
        clearTimeout(this.inactivityTimer)
      }
      
      // 5分钟无操作自动收缩
      this.inactivityTimer = setTimeout(() => {
        if (this.windowState.isExpanded) {
          this.toggleWindowSize()
          this.showNotification('长时间无操作，窗体已自动收缩', 'info')
        }
      }, 5 * 60 * 1000)
    },

    // 初始化窗体状态
    async initializeWindowState() {
      try {
        const state = await GetWindowState()
        this.windowState = state
      } catch (error) {
        console.error('获取窗体状态失败:', error)
      }
    },
    
    async addPatient(name) {
      const result = await this.patientStore.addPatient(name)
      if (result.success) {
        this.notificationStore.showSuccess(result.message)
        this.addLog({
          time: new Date().toLocaleString(),
          action: '添加患者',
          user: name,
          result: '成功'
        })
      } else {
        this.notificationStore.showError(result.message)
      }
    },
    
    async removePatient(index) {
      const result = await this.patientStore.removePatient(index)
      if (result.success) {
        this.notificationStore.showSuccess(result.message)
        this.addLog({
          time: new Date().toLocaleString(),
          action: '移除患者',
          user: result.patientName || '未知',
          result: '成功'
        })
      } else {
        this.notificationStore.showError(result.message)
      }
    },
    
    async clearPatients() {
      const result = await this.patientStore.clearPatients()
      if (result.success) {
        this.notificationStore.showSuccess(result.message)
        this.addLog({
          time: new Date().toLocaleString(),
          action: '清空患者列表',
          user: '系统',
          result: '成功'
        })
      } else {
        this.notificationStore.showError(result.message)
      }
    },
    
    handleProcessComplete(result) {
      this.notificationStore.showSuccess(`处理完成: ${result}`)
    },
    
    addLog(log) {
      this.logs.unshift(log)
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(0, 100)
      }
    },
    
    showLoading(message) {
      this.loading = true
      this.loadingMessage = message
    },
    
    hideLoading() {
      this.loading = false
      this.loadingMessage = ''
    },
    

    
    startTimeUpdate() {
      this.updateTime()
      this.timeInterval = setInterval(this.updateTime, 1000)
    },
    
    updateTime() {
      this.currentTime = new Date().toLocaleString()
    },
    
    setupKeyboardShortcuts() {
      // 监听键盘事件
      document.addEventListener('keydown', (event) => {
        // F9-F12 快捷键处理（保留窗口控制功能）
        if (['F9', 'F10', 'F11', 'F12'].includes(event.key)) {
          event.preventDefault()
          this.handleFunctionKey(event.key)
        }
        
        // Ctrl+Shift 组合键处理
        if (event.ctrlKey && event.shiftKey) {
          event.preventDefault()
          this.handleHotkeyCombo(event.key.toUpperCase())
        }
      })
    },
    
    async handleFunctionKey(key) {
      const keyMap = {
        'F9': () => this.minimizeWindow(),
        'F10': () => this.toggleWindowPosition(),
        // 'F11': () => this.toggleWindowSize(), // 展开功能暂时注释掉，近期不开发
        'F12': () => this.clearPatients()
      };
      
      if (keyMap[key]) {
        await keyMap[key]()
        // 同时调用后端的快捷键处理
        try {
          await HandleKeyboardShortcut(key)
        } catch (error) {
          console.error('后端快捷键处理失败:', error)
        }
      }
    },
    
    async handleHotkeyCombo(key) {
      const hotkeyMap = {
        'A': () => this.handleScreenshot('器官问题来源分析'),
        'B': () => this.handleScreenshot('生化平衡分析'),
        'C': () => this.handleScreenshot('病理形态学分析'),
        // 'U': () => this.uploadLatestScreenshot() // 已注释，功能由A、B、C快捷键替代
      };
      
      if (hotkeyMap[key]) {
        await hotkeyMap[key]()
        // 同时调用后端的快捷键处理
        try {
          await HandleKeyboardShortcut(`Ctrl+Shift+${key}`)
        } catch (error) {
          console.error('后端快捷键处理失败:', error)
        }
      }
    },

    // 配置监听器方法
    startConfigWatcher() {
      // console.log('配置监听器已移除定时获取逻辑，仅在初始化时获取配置')
      // // 每30秒检查一次配置更新
      // this.configWatcherTimer = setInterval(async () => {
      //   try {
      //     const newConfig = await GetConfig()
      //     // 检查站点信息是否有变化
      //     if (this.hasConfigChanged(newConfig)) {
      //       const oldSiteInfo = this.config?.SiteInfo || {}
      //       this.config = newConfig
      //       this.showNotification(`站点信息已更新: ${newConfig.SiteInfo?.SiteName || '未知站点'}`, 'success')
      //       console.log('配置已更新:', {
      //         旧站点: oldSiteInfo.SiteName || '未设置',
      //         新站点: newConfig.SiteInfo?.SiteName || '未设置'
      //       })
      //     }
      //   } catch (error) {
      //     console.error('检查配置更新失败:', error)
      //   }
      // }, 30000) // 30秒检查一次
    },

    // 检查配置是否有变化
    hasConfigChanged(newConfig) {
      if (!this.config || !newConfig) return true
      
      const oldSiteInfo = this.config.SiteInfo || {}
      const newSiteInfo = newConfig.SiteInfo || {}
      
      return (
        oldSiteInfo.SiteID !== newSiteInfo.SiteID ||
        oldSiteInfo.SiteName !== newSiteInfo.SiteName ||
        oldSiteInfo.SiteType !== newSiteInfo.SiteType ||
        oldSiteInfo.ParentOrg !== newSiteInfo.ParentOrg
      )
    },

    // 候检者相关方法
    async loadRegistrations() {
      try {
        this.registrations = [] // 获取数据前先清空
        const today = new Date().toISOString().split('T')[0] // 格式: YYYY-MM-DD
        // 使用新的GetPendingPatients函数，只获取候检者（已报到未检测）
        const newRegistrations = await GetPendingPatients(today)
        this.registrations = newRegistrations || [] // 如果API返回null或undefined，则设置为空数组
        // 按报到时间倒序排列（最新的在前面）
        this.registrations.sort((a, b) => new Date(b.register_time) - new Date(a.register_time))
        // 重置显示限制
        this.displayLimit = 5
        // 默认选中第一个候检者
        if (this.registrations.length > 0) {
          this.selectedPatientIndex = 0
        } else {
          this.selectedPatientIndex = -1
        }
        console.log('候检者列表（已报到未检测）:', this.registrations)
      } catch (error) {
        console.error('加载候检者列表失败:', error)
        this.showNotification('加载候检者列表失败: ' + error, 'error')
        this.registrations = []
        this.displayLimit = 5
        this.selectedPatientIndex = -1
      }
    },

    async refreshRegistrations() {
      if (this.isRefreshing) return
      
      this.isRefreshing = true
      try {
        await this.loadRegistrations()
        this.showNotification('候检者列表已刷新', 'success')
      } catch (error) {
        this.showNotification('刷新失败: ' + error, 'error')
      } finally {
        this.isRefreshing = false
      }
    },



    formatTime(timeStr) {
      if (!timeStr) return ''
      // 后端已经格式化为 "2006-01-02 15:04:05" 格式，直接显示月日时分
      try {
        if (timeStr.includes('-') && timeStr.includes(':')) {
          // 解析 "2006-01-02 15:04:05" 格式
          const parts = timeStr.split(' ')
          if (parts.length === 2) {
            const datePart = parts[0].split('-')
            const timePart = parts[1].split(':')
            if (datePart.length === 3 && timePart.length >= 2) {
              return `${datePart[1]}/${datePart[2]} ${timePart[0]}:${timePart[1]}`
            }
          }
        }
        return timeStr
      } catch (error) {
        return timeStr
      }
    },
    
    // 更新操作状态提示
    updateOperationStatus(message) {
      this.currentOperationStatus = message
      
      // 简化的进度处理，不再使用轮次概念
       const progressMatch = message.match(/(B02生化分析|C03病理分析)/)
       if (progressMatch) {
         const mode = progressMatch[1].includes('B02') ? 'B' : 'C'
         this.currentMode = mode
         // 简化的进度显示，不再计算轮次
         this.currentProgress = 50 // 固定显示50%进度
       } else {
         // 非进度消息，重置进度
         this.currentProgress = 0
         this.currentMode = ''
       }
    },
    
    // 处理通知显示事件
    handleNotificationShown(notificationData) {
      // 将最新的通知消息显示在操作状态区
      this.updateOperationStatus(notificationData.message)
      
      // 记录通知日志
      this.addLog(`Toast通知: ${notificationData.message}`, 'info')
    },
    
    // 处理通知移除事件
    handleNotificationRemoved(notificationData) {
      console.log('[App] Toast通知已移除:', notificationData)
    },
    
    // 处理所有通知清除事件
    handleAllNotificationsCleared() {
      console.log('[App] 所有Toast通知已清除')
      this.updateOperationStatus('所有通知已清除')
    },
    
    // 显示业务相关的Toast通知
    showBusinessToast(templateName, overrides = {}) {
      const config = getToastConfig(templateName, overrides)
      if (this.$refs.toastNotification) {
        return this.$refs.toastNotification.showToast(config)
      }
      return null
    },
    
    // 显示操作成功通知
    showSuccessToast(message, title = '操作成功') {
      return this.showBusinessToast(TOAST_TEMPLATES.OPERATION_SUCCESS, {
        title,
        message
      })
    },
    
    // 显示操作失败通知
    showErrorToast(message, title = '操作失败') {
      return this.showBusinessToast(TOAST_TEMPLATES.OPERATION_ERROR, {
        title,
        message
      })
    },
    
    // 显示警告通知
    showWarningToast(message, title = '警告') {
      return this.showBusinessToast(TOAST_TEMPLATES.OPERATION_ERROR, {
        type: 'warning',
        title,
        message
      })
    },
    
    // 初始化Toast管理器
    initializeToastManager() {
      try {
        // 等待下一个tick确保Toast组件已完全挂载
        this.$nextTick(() => {
          if (this.$refs.toastNotification) {
            toastManager.init(this.$refs.toastNotification)
            console.log('[App] Toast管理器初始化成功')
            
            // 注册Toast事件监听器
            toastManager.on('toast-shown', (data) => {
              console.log('[App] Toast显示:', data)
            })
            
            toastManager.on('toast-removed', (data) => {
              console.log('[App] Toast移除:', data)
            })
            
            // 显示初始化成功通知
            toastManager.success('应用程序已启动', '系统就绪')
          } else {
            console.error('[App] Toast组件引用未找到')
          }
        })
      } catch (error) {
        console.error('[App] Toast管理器初始化失败:', error)
      }
    },
    
    // 初始化事件管理器和通知管理器
    initializeEventAndNotificationManagers() {
      try {
        // 初始化事件管理器
        eventManager.initialize()
        console.log('[App] 事件管理器初始化成功')
        
        // 等待Toast组件挂载后初始化通知管理器
        this.$nextTick(() => {
          if (this.$refs.toastNotification) {
            notificationManager.initialize(this.$refs.toastNotification)
            console.log('[App] 通知管理器初始化成功')
            
            // 注册通知管理器的事件监听器
            notificationManager.on('progress-updated', (data) => {
              this.updateOperationStatus(`${data.message} (${data.progress}%)`)
            })
            
            notificationManager.on('task-completed', (data) => {
              this.updateOperationStatus(`任务完成: ${data.message}`)
            })
            
            notificationManager.on('task-error', (data) => {
              this.updateOperationStatus(`任务错误: ${data.message}`)
            })
            
            notificationManager.on('system-status', (data) => {
              this.updateOperationStatus(data.message)
            })
          } else {
            console.error('[App] Toast组件引用未找到，无法初始化通知管理器')
          }
        })
      } catch (error) {
        console.error('[App] 事件管理器和通知管理器初始化失败:', error)
      }
    },

    // 任务管理器状态相关方法
    toggleTaskManagerStatus() {
      console.log('[App] 切换任务管理器状态，当前状态:', this.showTaskManagerStatus)
      this.showTaskManagerStatus = !this.showTaskManagerStatus
      console.log('[App] 新状态:', this.showTaskManagerStatus)
      if (this.showTaskManagerStatus) {
        this.updateTaskStats()
      }
    },

    handleTaskManagerClose() {
      console.log('[App] 接收到任务管理器关闭事件')
      this.showTaskManagerStatus = false
    },

    async updateTaskStats() {
      try {
        if (window.go && window.go.main && window.go.main.App && window.go.main.App.GetTaskManagerStatus) {
          const status = await window.go.main.App.GetTaskManagerStatus()
          if (status && status.stats) {
            this.taskStats = {
              active_count: status.stats.active_count || 0,
              queue_size: status.stats.queue_size || 0,
              total_submitted: status.stats.total_submitted || 0,
              completed_tasks: status.stats.completed_tasks || 0,
              failed_tasks: status.stats.failed_tasks || 0,
              cancelled_tasks: status.stats.cancelled_tasks || 0
            }
          }
        }
      } catch (error) {
         console.error('更新任务统计失败:', error)
       }
     },

     startTaskStatsUpdate() {
       // 立即更新一次
       this.updateTaskStats()
       // 每5秒更新一次任务统计
       this.taskStatsTimer = setInterval(() => {
         this.updateTaskStats()
       }, 5000)
     },

     stopTaskStatsUpdate() {
       if (this.taskStatsTimer) {
         clearInterval(this.taskStatsTimer)
         this.taskStatsTimer = null
       }
     },

     // 缓存管理相关方法
     // 初始化缓存管理机制
     initializeCacheManagement() {
       console.log('[缓存管理] 初始化缓存清理机制')
       
       // 设置当前日期
       this.currentDate = new Date().toDateString()
       this.lastCacheCleanDate = localStorage.getItem('lastCacheCleanDate')
       
       // 立即执行一次日期检查
       this.checkDateAndClearCache()
       
       // 设置定时器，每小时检查一次日期变化
       this.dailyCacheTimer = setInterval(() => {
         this.checkDateAndClearCache()
       }, 60 * 60 * 1000) // 每小时检查一次
       
       console.log('[缓存管理] 缓存清理机制已启动，每小时检查一次日期变化')
     },

     // 检查日期变化并清理缓存
     checkDateAndClearCache() {
       const today = new Date().toDateString()
       
       // 如果日期发生变化，清理过期缓存
       if (this.currentDate !== today || this.lastCacheCleanDate !== today) {
         console.log('[缓存管理] 检测到日期变化，开始清理过期缓存')
         console.log('[缓存管理] 上次清理日期:', this.lastCacheCleanDate)
         console.log('[缓存管理] 当前日期:', today)
         
         this.clearExpiredPatientCache()
         this.currentDate = today
         this.lastCacheCleanDate = today
         localStorage.setItem('lastCacheCleanDate', today)
         
         // 显示缓存清理通知
         if (this.$refs.toastNotification) {
           this.$refs.toastNotification.showToast({
             title: '缓存已更新',
             message: '检测到日期变化，已清理过期患者数据缓存',
             type: 'info',
             duration: 3000
           })
         }
       }
     },

     // 清理过期的患者缓存数据
     clearExpiredPatientCache() {
       console.log('[缓存管理] 开始清理过期患者缓存数据')
       
       try {
         // 清理当前患者信息（如果不在今日待检列表中）
         this.clearCurrentPatientIfExpired()
         
         // 强制刷新患者列表
         this.forceRefreshPatientLists()
         
         // 清理本地存储中的过期数据
         this.clearExpiredLocalStorage()
         
         console.log('[缓存管理] 过期患者缓存数据清理完成')
       } catch (error) {
         console.error('[缓存管理] 清理过期缓存时发生错误:', error)
       }
     },

     // 清理当前患者信息（如果已过期）
     clearCurrentPatientIfExpired() {
       if (this.currentPatient) {
         console.log('[缓存管理] 检查当前患者是否过期:', this.currentPatient)
         
         // 检查当前患者是否在今日待检列表中
         const isInPendingList = this.pendingRegistrations.some(patient => 
           patient.number === this.currentPatient.number
         )
         
         if (!isInPendingList) {
           console.log('[缓存管理] 当前患者不在待检列表中，清理当前患者信息')
           
           // 清理当前患者信息
           this.patientStore.clearCurrentPatient()
           
           // 显示过期提示
           if (this.$refs.toastNotification) {
             this.$refs.toastNotification.showToast({
               title: '患者信息已更新',
               message: `患者 ${this.currentPatient.name} 已不在今日待检列表中，已清理显示`,
               type: 'warning',
               duration: 5000
             })
           }
         }
       }
     },

     // 强制刷新所有患者列表
     async forceRefreshPatientLists() {
       console.log('[缓存管理] 强制刷新所有患者列表')
       
       try {
         // 并行刷新所有列表
         await Promise.all([
           this.loadPendingRegistrations(),
           this.loadCompletedPatients(),
           this.loadUnanalyzedPatients(),
           this.patientStore.loadRegistrations()
         ])
         
         console.log('[缓存管理] 所有患者列表刷新完成')
       } catch (error) {
         console.error('[缓存管理] 刷新患者列表时发生错误:', error)
       }
     },

     // 清理本地存储中的过期数据
     clearExpiredLocalStorage() {
       console.log('[缓存管理] 清理本地存储中的过期数据')
       
       try {
         // 清理可能存在的过期患者相关缓存
         const keysToCheck = [
           'currentPatientCache',
           'patientListCache',
           'lastSelectedPatient',
           'pendingPatientsCache',
           'completedPatientsCache'
         ]
         
         keysToCheck.forEach(key => {
           if (localStorage.getItem(key)) {
             localStorage.removeItem(key)
             console.log(`[缓存管理] 已清理本地存储项: ${key}`)
           }
         })
       } catch (error) {
         console.error('[缓存管理] 清理本地存储时发生错误:', error)
       }
     },

     // 检查并更新当前患者状态
     checkAndUpdateCurrentPatientStatus() {
       console.log('[状态检查] 开始检查当前患者状态')
       
       if (!this.currentPatient) {
         console.log('[状态检查] 当前无患者信息')
         return
       }
       
       // 检查当前患者是否在待检列表中
       const isInPendingList = this.pendingRegistrations.some(patient => 
         patient.number === this.currentPatient.number
       )
       
       if (!isInPendingList) {
         console.log('[状态检查] 当前患者不在待检列表中，显示过期警告')
         
         // 在UI中显示过期警告
         this.showPatientExpiredWarning()
       } else {
         console.log('[状态检查] 当前患者状态正常')
       }
     },

     // 显示患者过期警告
     showPatientExpiredWarning() {
       if (this.$refs.toastNotification) {
         this.$refs.toastNotification.showToast({
           title: '患者信息提醒',
           message: `当前显示的患者 "${this.currentPatient.name}" 可能已不在今日待检列表中，请确认患者状态`,
           type: 'warning',
           duration: 8000,
           showProgress: false
         })
       }
     },

     // 手动触发缓存清理（用于调试或手动操作）
     manualClearCache() {
       console.log('[缓存管理] 手动触发缓存清理')
       this.clearExpiredPatientCache()
       
       if (this.$refs.toastNotification) {
         this.$refs.toastNotification.showToast({
           title: '缓存清理完成',
           message: '已手动清理所有过期患者缓存数据',
           type: 'success',
           duration: 3000
         })
       }
     }
  }
}

</script>

<style>
/* 基础样式 */
#app {
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: #f5f7fa;
  color: #2c3e50;
  transition: all 0.3s ease;
}

/* 紧凑模式样式 */
#app.compact {
  width: 100%; /* Make it responsive */
  background: linear-gradient(145deg, #ffffff, #f0f2f5);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
}



/* 紧凑模式头部 */
.compact-header {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  overflow: hidden;
}

.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.app-title {
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.window-controls {
  display: flex;
  gap: 4px;
}

.window-controls button {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.window-controls button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}



.compact-status {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  color: #2c3e50;
  flex-shrink: 0;
}



.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #2c3e50;
  flex: 1;
}

.status-item .label {
  opacity: 0.8;
  font-weight: 500;
  color: #34495e;
}

.status-item .value {
  font-weight: 600;
  background: rgba(52, 73, 94, 0.1);
  color: #2c3e50;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  border: 1px solid rgba(52, 73, 94, 0.2);
}

/* 紧凑模式内容 */
.compact-content {
  padding: 12px; /* 调整为统一的12px边距，确保左右边距一致 */
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: calc(100vh - 60px);
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;
}

/* 报到二维码容器 */
.registration-qrcode-container {
  background: white;
  border-radius: 8px;
  padding: 4px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.registration-qrcode-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
}


/* 操作状态区域 */
.operation-status {
  position: relative;
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border-radius: 8px;
  padding: 10px 12px;
  border-left: 4px solid #2196f3;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #2196f3;
  transition: width 0.3s ease;
  opacity: 0.3;
  z-index: 1;
}

.operation-text {
  position: relative;
  z-index: 2;
  font-size: 13px;
  font-weight: 500;
  color: #1976d2;
  flex: 1;
}

.operation-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.operation-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 6px 8px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  min-width: 80px;
  line-height: 1.2;
  text-align: center;
  transition: all 0.2s ease;
}

.capture-btn {
  background: #2196f3;
  color: white;
}

.capture-btn:hover {
  background: #1976d2;
}

.capture-btn:disabled {
  background: #cccccc;
  color: #666666;
  cursor: not-allowed;
  opacity: 0.6;
}

.capture-btn:disabled:hover {
  background: #cccccc;
  transform: none;
}

.submit-btn {
  background: #4caf50;
  color: white;
}

.submit-btn:hover {
  background: #45a049;
}



/* 快速操作按钮 */
.quick-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px;
  padding: 8px 6px; /* 缩小1/3，原来12px 8px现在8px 6px */
  border: none;
  border-radius: 8px;
  background: linear-gradient(145deg, #e3f2fd, #bbdefb);
  color: #1976d2;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 10px; /* 稍微缩小字体 */
  font-weight: 500;
}

.action-btn.primary {
  background: linear-gradient(145deg, #4caf50, #45a049);
  color: white;
}

.action-btn.expand-main {
  background: linear-gradient(145deg, #ff9800, #f57c00);
  color: white;
  border: 2px solid #ff6f00;
  font-weight: 600;
  position: relative;
}

.action-btn.expand-main::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ff9800, #f57c00, #ff9800);
  border-radius: 10px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-btn.expand-main:hover::before {
  opacity: 0.3;
}

.action-btn.expand-main:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn:active {
  transform: translateY(0);
}

.quick-actions button.secondary {
  background-color: #e0e0e0;
  color: #333;
}


/* 操作状态提示区 */
.operation-status {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 8px;
  text-align: center;
  min-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.operation-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}



.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-patient-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-patient-name {
  font-weight: 600;
  font-size: 16px;
}

.current-patient-number {
  font-size: 12px;
  opacity: 0.9;
}

.empty-text {
  font-size: 12px;
  text-align: center;
  width: 100%;
}

/* 患者列表紧凑版 */
.compact-patients {
  background: white;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

/* 候检者列表容器 */
.patient-section {
  background: white;
  border-radius: 8px;
  padding: 8px;
  margin-bottom: 10px;
  margin-right: 0px; /* 移除额外的右边距，让容器内容区域的padding来控制边距 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 600;
  color: #555;
}

.refresh-btn {
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 50%;
  background: #2196f3;
  color: white;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: #1976d2;
  transform: scale(1.1) rotate(180deg);
}

.refresh-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.patient-list {
  max-height: 200px;
  overflow-y: auto;
}

 .patient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  margin-bottom: 4px;
  margin-right: 4px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 11px;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.patient-item:hover {
  background: #e9ecef;
  transform: translateX(2px);
}

.patient-item.selected {
  background: #e3f2fd;
  border: 2px solid #2196f3;
  transform: translateX(2px);
}

.patient-radio {
  margin-right: 8px;
  cursor: pointer;
}

.patient-name {
  font-weight: 600;
  font-size: 16px;
  color: #2c3e50;
}

.patient-number {
  color: #2c3e50;
  font-size: 12px;
  font-weight: 600;
}

.patient-time {
  color: #6c757d;
  font-size: 10px;
}

.remove-btn {
  width: 16px;
  height: 16px;
  border: none;
  border-radius: 50%;
  background: #dc3545;
  color: white;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background: #c82333;
  transform: scale(1.1);
}

.more-patients {
  text-align: center;
  color: #2196f3;
  font-size: 10px;
  padding: 6px 8px;
  cursor: pointer;
  background: #f0f8ff;
  border-radius: 4px;
  margin-top: 4px;
  transition: all 0.2s ease;
}

.more-patients:hover {
  background: #e3f2fd;
  color: #1976d2;
  transform: translateY(-1px);
}

/* 二维码显示紧凑版 */
.compact-qrcode {
  background: white;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.qrcode-display {
  margin-top: 8px;
}

.qrcode-display img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

/* 展开模式样式 */
.expanded-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.expanded-header {
  background: rgba(255, 255, 255, 0.95);
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.expanded-header h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 700;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #555;
}



.stats {
  display: flex;
  gap: 30px;
  font-size: 14px;
  color: #555;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 12px;
  padding: 20px;
  min-width: 300px;
  max-width: 90vw;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dialog h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 18px;
}

.dialog input {
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  box-sizing: border-box;
}

.dialog input:focus {
  outline: none;
  border-color: #4facfe;
  box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.2);
}

.dialog-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 15px;
}

.dialog-buttons button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.dialog-buttons button[type="button"] {
  background: #6c757d;
  color: white;
}

.dialog-buttons button[type="submit"] {
  background: #4caf50;
  color: white;
}

.dialog-buttons button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.header h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.status-bar {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #666;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}



.footer {
  background: rgba(255, 255, 255, 0.95);
  padding: 10px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.stats {
  display: flex;
  gap: 30px;
  font-size: 14px;
  color: #666;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 1001;
  animation: slideIn 0.3s ease-out;
  max-width: 90vw;
  width: auto;
  box-sizing: border-box;
  word-wrap: break-word;
}

.notification.success {
  background: #27ae60;
}

.notification.error {
  background: #e74c3c;
}

.notification.info {
  background: #3498db;
}

.notification.warning {
  background: #f39c12;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 基础样式 */
#app {
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: #f5f7fa;
  color: #2c3e50;
  transition: all 0.3s ease;
}

/* 紧凑模式样式 */
#app.compact {
  width: 100%;
  min-width: 320px;
  background: linear-gradient(145deg, #ffffff, #f0f2f5);
  border-radius: 12px;
}

/* 处理中对话框样式 */
.processing-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  backdrop-filter: blur(8px);
}

.processing-dialog {
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  border-radius: 20px;
  padding: 0;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 400px;
  max-width: 90vw;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  animation: dialogSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.processing-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.processing-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.processing-spinner {
  color: white;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.processing-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.processing-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.organ-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 12px;
  color: white;
}

.organ-label {
  font-size: 14px;
  opacity: 0.9;
}

.organ-name {
  font-size: 16px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.step-text {
  font-weight: 500;
}

.progress-percent {
  font-weight: 600;
  color: #667eea;
}

.progress-bar {
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.processing-status {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  border-left: 4px solid #667eea;
}

.status-dots {
  display: flex;
  gap: 4px;
}

.dot {
  width: 6px;
  height: 6px;
  background: #667eea;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  40% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.status-text {
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
}

.processing-footer {
  padding: 20px 24px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
}

.cancel-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.cancel-btn:active {
  transform: translateY(0);
}

/* 通知设置面板样式 */
.notification-settings-panel {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.notification-settings-panel h3 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-settings-panel h3::before {
  content: "🔔";
  font-size: 18px;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.setting-item label {
  font-weight: 500;
  color: #495057;
  min-width: 80px;
}

.notification-select {
  flex: 1;
  padding: 8px 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.notification-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.notification-select:hover {
  border-color: #007bff;
}

.setting-description {
  margin-top: 8px;
}

.setting-description p {
  margin: 0;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.4;
}

.system-mode {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.app-mode {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

/* Tab选项卡样式 */
.patient-tabs-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 0;
}

.tab-navigation {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 8px;
  gap: 8px;
  align-items: center;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.tab-button:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.tab-button.active {
  background: #667eea;
  color: white;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.tab-icon {
  font-size: 16px;
}

.tab-text {
  font-weight: 600;
}

.refresh-btn {
  margin-left: auto;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.refresh-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #667eea;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.tab-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.tab-panel {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

.patient-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.patient-item:hover {
  border-color: #667eea;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.1);
}

.patient-item.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.patient-item.completed {
  background: #f8f9fa;
  cursor: default;
}

.patient-item.completed:hover {
  border-color: #28a745;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.1);
}

.patient-item.unanalyzed {
  background: #fff8e1;
  cursor: default;
}

.patient-item.unanalyzed:hover {
  border-color: #ff9800;
  box-shadow: 0 2px 4px rgba(255, 152, 0, 0.1);
}

.patient-radio {
  margin: 0;
}

.patient-name {
  font-weight: 600;
  color: #333;
  min-width: 80px;
}

.patient-age {
  color: #666;
  font-size: 13px;
  min-width: 40px;
}

.patient-gender {
  color: #666;
  font-size: 13px;
  min-width: 30px;
}

.patient-number {
  color: #667eea;
  font-size: 13px;
  font-weight: 500;
  min-width: 100px;
}

.patient-time {
  color: #999;
  font-size: 12px;
  min-width: 80px;
}

.patient-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.patient-status.pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.patient-status.completed {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.patient-status.unanalyzed {
  background: #fff3e0;
  color: #e65100;
  border: 1px solid #ffcc80;
}

.completion-time {
  color: #28a745;
  font-size: 12px;
  font-weight: 500;
  min-width: 80px;
}

.more-patients {
  text-align: center;
  padding: 16px;
  color: #667eea;
  cursor: pointer;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-weight: 500;
}

.more-patients:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.empty-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
}
/* 左侧二维码列 - 调整为更小的固定宽度 */
.qrcode-column {
  flex: 0 0 120px;
  min-width: 0;
}

/* 右侧状态信息列 - 占2/3宽度 */
.status-column {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.qrcode-column .registration-qrcode-container {
  margin: 0;
  padding: 4px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 112px;
  height: 112px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 底部工具栏样式 */
.bottom-toolbar {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
}

.task-manager-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.task-manager-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.task-manager-btn.active {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  transform: scale(1.05);
}

.task-icon {
  flex-shrink: 0;
}

.btn-text {
  white-space: nowrap;
}

.task-badge {
  background: #ff4757;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 700;
  position: absolute;
  top: -5px;
  right: -5px;
  border: 2px solid white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bottom-toolbar {
    bottom: 10px;
    right: 10px;
  }
  
  .task-manager-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
  
  .btn-text {
    display: none;
  }
}

</style>
