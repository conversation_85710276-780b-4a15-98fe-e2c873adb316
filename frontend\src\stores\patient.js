import { defineStore } from 'pinia'
import { GetRegistrations, SetCurrentPatientIndex } from '../../wailsjs/go/main/App'

export const usePatientStore = defineStore('patient', {
  state: () => ({
    // 候检者列表
    registrations: [],
    // 当前选中的候检者索引
    selectedPatientIndex: 0,
    // 显示限制
    displayLimit: 5,
    // 刷新状态
    isRefreshing: false,
    // 轮询定时器
    pollTimer: null,
    // 患者列表（展开模式用）
    patients: []
  }),

  getters: {
    // 当前选中的候检者
    currentPatient: (state) => {
      if (state.registrations.length > 0 && 
          state.selectedPatientIndex >= 0 && 
          state.selectedPatientIndex < state.registrations.length) {
        return state.registrations[state.selectedPatientIndex]
      }
      return null
    },

    // 显示的候检者列表
    displayedRegistrations: (state) => {
      return state.registrations.slice(0, state.displayLimit)
    },

    // 今日患者数量
    todayPatientCount: (state) => {
      return state.registrations.length
    },

    // 当前挂号号码
    currentRegistrationNumber: (state) => {
      return state.patients.length + 1
    }
  },

  actions: {
    // 加载候检者列表
    async loadRegistrations() {
      try {
        this.registrations = [] // 获取数据前先清空
        const today = new Date().toISOString().split('T')[0] // 格式: YYYY-MM-DD
        const newRegistrations = await GetRegistrations(today)
        this.registrations = newRegistrations || [] // 如果API返回null或undefined，则设置为空数组
        
        // 按报到时间正序排列（先报到的在前面）
        this.registrations.sort((a, b) => new Date(a.register_time) - new Date(b.register_time))
        
        // 重置显示限制
        this.displayLimit = 5
        
        // 默认选中第一个候检者
        if (this.registrations.length > 0) {
          this.selectedPatientIndex = 0
        } else {
          this.selectedPatientIndex = -1
        }
      } catch (error) {
        console.error('加载候检者列表失败:', error)
        this.registrations = []
        this.displayLimit = 5
        this.selectedPatientIndex = -1
        throw error
      }
    },

    // 刷新候检者列表
    async refreshRegistrations() {
      if (this.isRefreshing) return
      
      this.isRefreshing = true
      try {
        await this.loadRegistrations()
        return { success: true, message: '候检者列表已刷新' }
      } catch (error) {
        return { success: false, message: '刷新失败: ' + error }
      } finally {
        this.isRefreshing = false
      }
    },

    // 选择候检者
    async selectPatient(index) {
      this.selectedPatientIndex = index
      
      // 同步到后端
      try {
        await SetCurrentPatientIndex(index)
        return { 
          success: true, 
          message: `已选择候检者: ${this.currentPatient?.name}` 
        }
      } catch (error) {
        console.error('设置当前患者索引失败:', error)
        return { success: false, message: '设置当前患者失败' }
      }
    },

    // 加载更多候检者
    loadMoreRegistrations() {
      this.displayLimit = Math.min(this.displayLimit + 5, this.registrations.length)
    },

    // 持续轮询 - 定时刷新待检测列表以发现新报到的患者
    async startConditionalPolling() {
      // 清除之前的定时器
      if (this.pollTimer) {
        clearInterval(this.pollTimer)
      }

      const pollInterval = 30000 // 30秒轮询一次，避免过于频繁
      let newPatientCount = 0

      this.pollTimer = setInterval(async () => {
        try {
          const oldCount = this.registrations.length
          const oldNames = this.registrations.map(r => r.name)

          await this.loadRegistrations()

          const newCount = this.registrations.length
          const newNames = this.registrations.map(r => r.name)

          // 检查是否有新患者报到
          const newPatients = newNames.filter(name => !oldNames.includes(name))

          if (newPatients.length > 0) {
            newPatientCount += newPatients.length
            console.log(`发现 ${newPatients.length} 位新报到患者:`, newPatients)
          }
        } catch (error) {
          console.error('轮询候检者列表失败:', error)
        }
      }, pollInterval)

      return { success: true, newCount: newPatientCount }
    },

    // 停止轮询
    stopPolling() {
      if (this.pollTimer) {
        clearInterval(this.pollTimer)
        this.pollTimer = null
      }
    },

    // 添加患者（展开模式用）
    addPatient(patient) {
      try {
        this.patients.push({
          id: Date.now(),
          ...patient,
          addedTime: new Date().toISOString()
        })
        return { success: true, message: `患者 ${patient.name || '未知'} 已添加` }
      } catch (error) {
        console.error('添加患者失败:', error)
        return { success: false, message: '添加患者失败: ' + error.message }
      }
    },

    // 移除患者
    removePatient(patientId) {
      try {
        const index = this.patients.findIndex(p => p.id === patientId)
        if (index > -1) {
          const patient = this.patients[index]
          this.patients.splice(index, 1)
          return { 
            success: true, 
            message: `患者已移除`,
            patientName: patient.name || '未知'
          }
        } else {
          return { success: false, message: '未找到指定患者' }
        }
      } catch (error) {
        console.error('移除患者失败:', error)
        return { success: false, message: '移除患者失败: ' + error.message }
      }
    },

    // 清空患者列表
    clearPatients() {
      try {
        this.patients = []
        return { success: true, message: '患者列表已清空' }
      } catch (error) {
        console.error('清空患者列表失败:', error)
        return { success: false, message: '清空失败: ' + error.message }
      }
    },

    // 格式化性别
    formatGender(gender) {
      if (gender === 1) {
        return '男'
      } else if (gender === 2) {
        return '女'
      } else {
        return '未知'
      }
    },

    // 计算年龄
    calculateAge(birthDate) {
      if (!birthDate) return ''
      const birth = new Date(birthDate)
      const today = new Date()
      let age = today.getFullYear() - birth.getFullYear()
      const m = today.getMonth() - birth.getMonth()
      if (m < 0 || (m === 0 && today.getDate() < birth.getDate())) {
        age--
      }
      return age + '岁'
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''
      try {
        if (timeStr.includes('-') && timeStr.includes(':')) {
          // 解析 "2006-01-02 15:04:05" 格式
          const parts = timeStr.split(' ')
          if (parts.length === 2) {
            const datePart = parts[0].split('-')
            const timePart = parts[1].split(':')
            if (datePart.length === 3 && timePart.length >= 2) {
              return `${datePart[1]}/${datePart[2]} ${timePart[0]}:${timePart[1]}`
            }
          }
        }
        return timeStr
      } catch (error) {
        return timeStr
      }
    }
  }
})