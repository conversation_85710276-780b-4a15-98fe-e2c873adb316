// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT
import {models} from '../models';
import {services} from '../models';

export function AddPatient(arg1:string):Promise<void>;

export function AutoCollapseAfterInactivity():Promise<void>;

export function CaptureScreenshotByBtn():Promise<Record<string, any>>;

export function ClearCompletedFailedTasks():Promise<void>;

export function ClearCurrentPatient():Promise<void>;

export function ClearPatientList():Promise<void>;

export function DiagnoseSystemStatus():Promise<Record<string, any>>;

export function ExportCurrentUserCheckingInfoJSON(arg1:string):Promise<Array<number>>;

export function ExtractOrganFromScreenshot(arg1:string):Promise<string>;

export function GenerateQRCode():Promise<string>;

export function GenerateRegistrationQRCode():Promise<Record<string, string>>;

export function GetCompletedPatients():Promise<Array<models.Registration>>;

export function GetCompletedPatientsByDate(arg1:string):Promise<Array<models.Registration>>;

export function GetConfig():Promise<models.AppConfig>;

export function GetCurrentPatient():Promise<models.Registration>;

export function GetCurrentPatientIndex():Promise<number>;

export function GetCurrentPatientInfo():Promise<Record<string, any>>;

export function GetCurrentPatientName():Promise<string>;

export function GetCurrentRegistrationNumber():Promise<number>;

export function GetCurrentUserCheckingInfo(arg1:string):Promise<models.CurrentUserCheckingInfo>;

export function GetFailedTasks():Promise<Record<string, any>>;

export function GetModeConfig():Promise<Record<string, models.ModeInfo>>;

export function GetPatientList():Promise<Array<models.Patient>>;

export function GetPendingPatients(arg1:string):Promise<Array<models.Registration>>;

export function GetPendingRegistrations():Promise<Array<models.Registration>>;

export function GetRegistrations(arg1:string):Promise<Array<models.Registration>>;

export function GetSiteInfo():Promise<models.SiteInfo>;

export function GetTaskManagerStatus():Promise<Record<string, any>>;

export function GetTodayPatientCount():Promise<number>;

export function GetUnanalyzedPatients(arg1:string):Promise<Array<models.Registration>>;

export function GetUserCheckingInfoStatus(arg1:string):Promise<Record<string, any>>;

export function GetWindowState():Promise<Record<string, any>>;

export function Greet(arg1:string):Promise<string>;

export function HandleCozeLLM_AnylizeD_value(arg1:string):Promise<void>;

export function HandleHotkey(arg1:string):Promise<void>;

export function HandleKeyboardShortcut(arg1:string):Promise<void>;

export function MarkPatientCompleted(arg1:string,arg2:string):Promise<void>;

export function MinimizeWindow():Promise<void>;

export function MoveToNextPatient():Promise<void>;

export function ProcessImageWithOCR(arg1:string):Promise<services.OCRResult>;

export function ProcessScreenshotWithOCR(arg1:string,arg2:string):Promise<Record<string, any>>;

export function ProcessScreenshotWorkflow(arg1:string):Promise<string>;

export function RemovePatient(arg1:number):Promise<void>;

export function SendConfigurableToast(arg1:string,arg2:string,arg3:Record<string, any>):Promise<void>;

export function SendTaskCompletedNotification(arg1:services.TaskResult):Promise<void>;

export function SetAlwaysOnTop(arg1:boolean):Promise<void>;

export function SetCompactWindow():Promise<void>;

export function SetCurrentPatientIndex(arg1:number):Promise<void>;

export function SetCurrentPatientName(arg1:string):Promise<void>;

export function SetExpandedWindow():Promise<void>;

export function SetWindowPosition(arg1:string):Promise<void>;

export function ShowErrorNotification(arg1:string,arg2:string,arg3:number):Promise<string>;

export function ShowInfoNotification(arg1:string,arg2:string,arg3:number):Promise<string>;

export function ShowOCRProcessNotification(arg1:string,arg2:string,arg3:number):Promise<void>;

export function ShowProgressNotification(arg1:string,arg2:string,arg3:number,arg4:number):Promise<string>;

export function ShowSuccessNotification(arg1:string,arg2:string,arg3:number):Promise<string>;

export function ShowToastNotification(arg1:string,arg2:string,arg3:string,arg4:number,arg5:boolean,arg6:number):Promise<void>;

export function ShowWailsNotification(arg1:string,arg2:string,arg3:string,arg4:number):Promise<string>;

export function ShowWarningNotification(arg1:string,arg2:string,arg3:number):Promise<string>;

export function SubmitScreenshotTask(arg1:services.TaskType,arg2:string,arg3:string):Promise<void>;

export function TakeConcurrentScreenshot(arg1:string):Promise<any>;

export function TakeOCRScreenshot(arg1:string,arg2:string):Promise<Record<string, any>>;

export function TestAllTasksCompletion(arg1:string):Promise<void>;

export function TestOCR(arg1:string):Promise<Record<string, any>>;

export function ToggleWindowSize():Promise<void>;

export function UpdateCropSettings(arg1:models.AppConfig):Promise<void>;

export function UpdateNotificationMode(arg1:boolean):Promise<void>;

export function UpdateOperationStatus(arg1:string,arg2:boolean):Promise<void>;

export function UpdateProgressBar(arg1:number,arg2:number,arg3:string):Promise<void>;

export function UpdateProgressNotification(arg1:string,arg2:number,arg3:string):Promise<void>;

export function UpdateSiteInfo(arg1:models.AppConfig):Promise<void>;

export function ValidateOCRSetup():Promise<void>;
