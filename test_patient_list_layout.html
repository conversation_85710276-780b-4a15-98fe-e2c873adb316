<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>患者列表布局调整测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            text-align: center;
        }
        
        .title {
            font-size: 32px;
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 18px;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .changes-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 25px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: left;
        }
        
        .changes-title {
            font-size: 22px;
            margin-bottom: 15px;
            color: #ffd700;
            text-align: center;
        }
        
        .change-item {
            margin: 12px 0;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 4px solid #10b981;
        }
        
        .change-item strong {
            color: #60a5fa;
        }
        
        .code {
            background: rgba(0, 0, 0, 0.4);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .demo-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 20px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .demo-title {
            font-size: 20px;
            margin-bottom: 15px;
            color: #ffd700;
            text-align: center;
        }
        
        .layout-demo {
            background: white;
            border-radius: 12px;
            height: 400px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
        }
        
        .demo-tabs {
            background: #f8f9fa;
            padding: 8px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            gap: 8px;
        }
        
        .demo-tab {
            padding: 8px 12px;
            background: #667eea;
            color: white;
            border-radius: 6px;
            font-size: 12px;
        }
        
        .demo-content {
            flex: 1;
            padding: 12px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .demo-list {
            flex: 1;
            overflow-y: auto;
            padding-right: 4px;
            margin-right: 4px;
        }
        
        .demo-item {
            background: #f8f9fa;
            padding: 8px;
            margin-bottom: 4px;
            margin-right: 8px;
            border-radius: 6px;
            font-size: 12px;
            color: #333;
            border: 1px solid #e9ecef;
        }
        
        .button {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔧 布局间距问题修复</h1>
        <p class="subtitle">黄色区域已下移，与红色区域保持适当间距，不再遮挡</p>
        
        <div class="changes-box">
            <div class="changes-title">🔧 布局间距修复内容</div>

            <div class="change-item">
                <strong>1. 主内容区域高度调整：</strong><br>
                修改 <span class="code">height: calc(100vh - 280px)</span> 为顶部红色区域预留更多空间<br>
                添加 <span class="code">margin-top: 12px</span> 与红色区域保持间距
            </div>

            <div class="change-item">
                <strong>2. 顶部状态区域：</strong><br>
                添加 <span class="code">min-height: 200px</span> 确保红色区域有足够高度<br>
                添加 <span class="code">box-sizing: border-box</span> 优化盒模型
            </div>

            <div class="change-item">
                <strong>3. 解决的问题：</strong><br>
                ✅ 黄色区域不再遮挡红色区域<br>
                ✅ 两个区域之间有适当间距<br>
                ✅ 布局更加稳定和美观
            </div>
        </div>
        
        <div class="demo-container">
            <div class="demo-title">📱 修复后的布局效果</div>
            <div style="background: white; border-radius: 12px; padding: 20px; color: #333;">
                <!-- 模拟红色区域 -->
                <div style="background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; padding: 15px; border-radius: 8px; margin-bottom: 12px; min-height: 120px; display: flex; align-items: center; justify-content: center;">
                    <div style="text-align: center;">
                        <h3 style="margin: 0; font-size: 18px;">🧠 AI磁感评估</h3>
                        <p style="margin: 5px 0 0 0; opacity: 0.9;">红色区域 - 不再被遮挡</p>
                    </div>
                </div>

                <!-- 模拟黄色区域 -->
                <div style="background: linear-gradient(135deg, #feca57, #ff9ff3); color: #333; padding: 15px; border-radius: 8px; min-height: 200px;">
                    <div style="text-align: center; margin-bottom: 10px;">
                        <h4 style="margin: 0; font-size: 16px;">📋 患者列表区域</h4>
                        <p style="margin: 5px 0 0 0; opacity: 0.8;">黄色区域 - 已下移并保持间距</p>
                    </div>
                    <div style="background: rgba(255,255,255,0.3); border-radius: 6px; padding: 10px; height: 120px; overflow-y: auto;">
                        <div style="background: rgba(255,255,255,0.6); margin: 2px 0; padding: 5px; border-radius: 4px; font-size: 12px;">刘昌军 男 250707001</div>
                        <div style="background: rgba(255,255,255,0.6); margin: 2px 0; padding: 5px; border-radius: 4px; font-size: 12px;">李明 男 250707002</div>
                        <div style="background: rgba(255,255,255,0.6); margin: 2px 0; padding: 5px; border-radius: 4px; font-size: 12px;">王强 男 250707003</div>
                        <div style="background: rgba(255,255,255,0.6); margin: 2px 0; padding: 5px; border-radius: 4px; font-size: 12px;">王明明 男 250707004</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <p style="font-size: 16px; opacity: 0.9;">
                ✅ 黄色区域不再遮挡红色区域<br>
                ✅ 两个区域之间有适当间距<br>
                ✅ 布局更加稳定和美观<br>
                ✅ 红色区域完全可见
            </p>
        </div>
        
        <button class="button" onclick="showDetails()">查看详细说明</button>
        <button class="button" onclick="window.close()">关闭页面</button>
    </div>
    
    <script>
        function showDetails() {
            alert(`🔧 布局间距问题修复详情：

🎯 主要问题：
黄色区域（患者列表）遮挡了红色区域（AI磁感评估）

🔧 修复方案：
1. 调整主内容区域高度计算
2. 为顶部红色区域预留足够空间
3. 添加适当的间距

✅ 具体修改：
- compact-content: height: calc(100vh - 280px)
- compact-content: margin-top: 12px
- compact-status: min-height: 200px

🎉 修复效果：
- 黄色区域不再遮挡红色区域
- 两个区域之间有12px间距
- 布局更加稳定和美观

现在请在实际应用中测试效果！`);
        }
        
        console.log('🔧 布局间距问题修复完成');
        console.log('主要修改：');
        console.log('1. compact-content: 调整高度计算，为顶部区域预留空间');
        console.log('2. compact-content: 添加顶部边距，与红色区域保持间距');
        console.log('3. compact-status: 设置最小高度，确保红色区域稳定');
        console.log('4. 解决黄色区域遮挡红色区域的问题');
        console.log('5. 布局更加稳定和美观');
    </script>
</body>
</html>
