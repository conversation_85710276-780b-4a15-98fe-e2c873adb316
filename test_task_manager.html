<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理器状态测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.2s ease;
        }
        
        .test-button:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .status-info {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        
        .task-manager-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(4px);
        }
        
        .task-manager-modal {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            color: white;
            animation: modalSlideIn 0.3s ease-out;
        }
        
        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .modal-title {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: white;
        }
        
        .close-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 8px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: white;
            transition: all 0.2s ease;
        }
        
        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }
        
        .modal-content {
            padding: 24px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #fff;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>任务管理器状态弹窗测试</h1>
        <p>这是一个测试页面，用于验证任务管理器状态弹窗的功能。</p>
        
        <button class="test-button" onclick="showModal()">显示任务管理器状态</button>
        <button class="test-button" onclick="hideModal()">隐藏弹窗</button>
        
        <div class="status-info">
            <h3>功能说明：</h3>
            <ul>
                <li>✅ 应用启动时弹窗处于隐藏状态</li>
                <li>✅ 底部右侧有一个"任务管理器状态"按钮</li>
                <li>✅ 点击按钮后弹出遮罩弹窗</li>
                <li>✅ 弹窗右上角有关闭按钮</li>
                <li>✅ 点击遮罩区域可以关闭弹窗</li>
                <li>✅ 弹窗显示任务统计信息</li>
            </ul>
        </div>
    </div>
    
    <!-- 任务管理器状态弹窗 -->
    <div id="taskManagerModal" class="task-manager-overlay hidden" onclick="handleOverlayClick(event)">
        <div class="task-manager-modal" onclick="event.stopPropagation()">
            <!-- 弹窗头部 -->
            <div class="modal-header">
                <h3 class="modal-title">任务管理器状态</h3>
                <button class="close-btn" onclick="hideModal()" title="关闭">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>
                    </svg>
                </button>
            </div>

            <!-- 弹窗内容 -->
            <div class="modal-content">
                <!-- 统计信息网格 -->
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-label">活跃任务</div>
                        <div class="stat-value">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">队列大小</div>
                        <div class="stat-value">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">总提交</div>
                        <div class="stat-value">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">已完成</div>
                        <div class="stat-value">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">失败任务</div>
                        <div class="stat-value">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">已取消</div>
                        <div class="stat-value">0</div>
                    </div>
                </div>

                <div style="text-align: center; color: rgba(255, 255, 255, 0.7); font-style: italic; padding: 24px;">
                    当前无活跃任务
                </div>
            </div>
        </div>
    </div>

    <script>
        let updateTimer = null;

        function showModal() {
            console.log('[Test] 显示弹窗');
            const modal = document.getElementById('taskManagerModal');
            modal.classList.remove('hidden');
            startUpdating();
        }

        function hideModal() {
            console.log('[Test] 隐藏弹窗');
            const modal = document.getElementById('taskManagerModal');
            modal.classList.add('hidden');
            stopUpdating();
        }

        function startUpdating() {
            console.log('[Test] 开始定时更新');
            updateTaskStatus(); // 立即更新一次
            if (updateTimer) {
                clearInterval(updateTimer);
            }
            updateTimer = setInterval(() => {
                console.log('[Test] 定时更新任务状态');
                updateTaskStatus();
            }, 2000);
        }

        function stopUpdating() {
            console.log('[Test] 停止定时更新');
            if (updateTimer) {
                clearInterval(updateTimer);
                updateTimer = null;
            }
        }

        function updateTaskStatus() {
            // 模拟数据更新
            const stats = document.querySelectorAll('.stat-value');
            stats.forEach((stat, index) => {
                const randomValue = Math.floor(Math.random() * 10);
                stat.textContent = randomValue;
            });
            console.log('[Test] 任务状态已更新');
        }

        // 测试键盘事件
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                console.log('[Test] ESC键按下');
                hideModal();
            }
        });

        // 测试遮罩点击
        function handleOverlayClick(event) {
            if (event.target.classList.contains('task-manager-overlay')) {
                console.log('[Test] 遮罩点击');
                hideModal();
            }
        }

        console.log('任务管理器状态弹窗测试页面已加载');
    </script>
</body>
</html>
