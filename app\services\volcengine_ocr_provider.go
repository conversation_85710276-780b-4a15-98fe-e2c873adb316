// Package services 火山引擎OCR提供者实现
package services

import (
	"context"
	"fmt"

	"MagneticOperator/app/models"
)

// VolcEngineOCRProvider 火山引擎OCR提供者
type VolcEngineOCRProvider struct {
	config    *models.VolcEngineOCRConfig
	client    *VolcEngineOCRClient
	sdkClient *VolcEngineSDKOCRClient
	organDB   *OrganDatabase
	useSDK    bool // 是否使用官方SDK
}

// NewVolcEngineOCRProvider 创建火山引擎OCR提供者
func NewVolcEngineOCRProvider(config *models.VolcEngineOCRConfig, organDB *OrganDatabase) *VolcEngineOCRProvider {
	return &VolcEngineOCRProvider{
		config:    config,
		client:    NewVolcEngineOCRClient(config),
		sdkClient: NewVolcEngineSDKOCRClient(config),
		organDB:   organDB,
		useSDK:    true, // 默认使用官方SDK
	}
}

// ProcessImage 处理图片并返回OCR结果
func (p *VolcEngineOCRProvider) ProcessImage(ctx context.Context, imagePath string) (*OCRResult, error) {
	fmt.Printf("[火山引擎OCR] 开始处理图片: %s\n", imagePath)

	// 根据配置选择使用SDK客户端或自定义客户端
	var result *OCRResult
	var err error

	if p.useSDK {
		fmt.Printf("[火山引擎OCR] 使用官方SDK客户端\n")
		result, err = p.sdkClient.ProcessImage(ctx, imagePath)
	} else {
		fmt.Printf("[火山引擎OCR] 使用自定义客户端\n")
		result, err = p.client.ProcessImage(ctx, imagePath)
	}

	if err != nil {
		fmt.Printf("[火山引擎OCR] 处理失败，错误详情: %v\n", err)
		return nil, fmt.Errorf("火山引擎OCR处理失败: %w", err)
	}

	// 使用器官数据库校准器官名称
	if p.organDB != nil && result.OrganName != "" {
		calibratedName := p.calibrateOrganName(result.OrganName)
		if calibratedName != result.OrganName {
			fmt.Printf("[火山引擎OCR] 器官名称校准: %s -> %s\n", result.OrganName, calibratedName)
			result.OrganName = calibratedName
		}
	}

	fmt.Printf("[火山引擎OCR] 处理完成，置信度: %.2f\n", result.Confidence)
	return result, nil
}

// GetProviderName 获取提供者名称
func (p *VolcEngineOCRProvider) GetProviderName() string {
	return "VolcEngine OCR"
}

// ValidateConfig 验证配置是否有效
func (p *VolcEngineOCRProvider) ValidateConfig() error {
	if p.config == nil {
		return fmt.Errorf("火山引擎OCR配置为空")
	}

	if p.config.APIURL == "" {
		return fmt.Errorf("火山引擎OCR API URL未配置")
	}

	if p.config.AccessKeyID == "" {
		return fmt.Errorf("火山引擎OCR AccessKeyID未配置")
	}

	if p.config.SecretAccessKey == "" {
		return fmt.Errorf("火山引擎OCR SecretAccessKey未配置")
	}

	fmt.Printf("[火山引擎OCR] 配置验证通过\n")
	return nil
}

// Close 关闭提供者
func (p *VolcEngineOCRProvider) Close() {
	fmt.Printf("[火山引擎OCR] 清理资源\n")
}

// SetUseSDK 设置是否使用官方SDK
func (p *VolcEngineOCRProvider) SetUseSDK(useSDK bool) {
	p.useSDK = useSDK
	if useSDK {
		fmt.Printf("[火山引擎OCR] 切换到官方SDK客户端\n")
	} else {
		fmt.Printf("[火山引擎OCR] 切换到自定义客户端\n")
	}
}

// IsUsingSDK 返回当前是否使用官方SDK
func (p *VolcEngineOCRProvider) IsUsingSDK() bool {
	return p.useSDK
}

// calibrateOrganName 校准器官名称
// 使用器官数据库进行名称标准化
func (p *VolcEngineOCRProvider) calibrateOrganName(rawOrganName string) string {
	if p.organDB == nil || len(p.organDB.Organs) == 0 {
		return rawOrganName
	}

	// 查找最佳匹配的器官名称
	bestMatch, similarity := p.findBestMatchOrgan(rawOrganName)

	// 如果相似度足够高，使用标准名称
	if similarity > 0.7 { // 相似度阈值
		return bestMatch
	}

	return rawOrganName
}

// findBestMatchOrgan 查找最佳匹配的器官名称
func (p *VolcEngineOCRProvider) findBestMatchOrgan(ocrOrganName string) (string, float64) {
	if p.organDB == nil || len(p.organDB.Organs) == 0 {
		return ocrOrganName, 0.0
	}

	bestMatch := ocrOrganName
	highestSimilarity := 0.0

	for _, organ := range p.organDB.Organs {
		// 计算与标准器官名称的相似度
		similarity := calculateSimilarity(ocrOrganName, organ.Name)

		if similarity > highestSimilarity {
			highestSimilarity = similarity
			bestMatch = organ.Name
		}
	}

	return bestMatch, highestSimilarity
}
