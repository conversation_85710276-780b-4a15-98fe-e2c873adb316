// Package services 提供火山引擎OCR API客户端
package services

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"net/url"
	"os"
	"time"

	json "github.com/goccy/go-json"
	"github.com/valyala/fasthttp"

	"MagneticOperator/app/models"
)

// VolcEngineOCRClient 火山引擎OCR客户端
type VolcEngineOCRClient struct {
	config *models.VolcEngineOCRConfig
	client *RetryableHTTPClient
}

// NewVolcEngineOCRClient 创建火山引擎OCR客户端
func NewVolcEngineOCRClient(config *models.VolcEngineOCRConfig) *VolcEngineOCRClient {
	return &VolcEngineOCRClient{
		config: config,
		client: NewRetryableHTTPClient(60 * time.Second),
	}
}

// decodeSecretKey 解码Base64编码的密钥（只做一次解码）
func (c *VolcEngineOCRClient) decodeSecretKey(encodedKey string) string {
	// 尝试Base64解码
	if decoded, err := base64.StdEncoding.DecodeString(encodedKey); err == nil {
		decodedStr := string(decoded)
		fmt.Printf("[火山引擎OCR] 检测到Base64编码的密钥，已解码\n")
		return decodedStr
	}
	// 不是Base64编码，直接返回原值
	fmt.Printf("[火山引擎OCR] 密钥未进行Base64编码，直接使用\n")
	return encodedKey
}

// VolcEngineOCRRequest 火山引擎OCR请求结构
type VolcEngineOCRRequest struct {
	ImageBase64 string `json:"image_base64"`
}

// VolcEngineOCRResponse 火山引擎OCR响应结构
type RectInfo struct {
	X      int `json:"x"`
	Y      int `json:"y"`
	Width  int `json:"width"`
	Height int `json:"height"`
}

// CharInfo 文字信息
type CharInfo struct {
	X      int     `json:"x"`
	Y      int     `json:"y"`
	Width  int     `json:"width"`
	Height int     `json:"height"`
	Score  float64 `json:"score"`
	Char   string  `json:"char"`
}

// VolcEngineOCRResponse 火山引擎OCR响应结构体
type VolcEngineOCRResponse struct {
	Code int `json:"code"`
	Data struct {
		Angle     float64      `json:"angle"`
		LineTexts []string     `json:"line_texts"`
		LineRects []RectInfo   `json:"line_rects"`
		LineProbs []float64    `json:"line_probs"`
		Chars     [][]CharInfo `json:"chars"`    // 2D数组：每行的字符信息
		Polygons  [][][]int    `json:"polygons"` // 3D数组：每行矩形框四点坐标
	} `json:"data"`
	Message     string `json:"message"`
	RequestID   string `json:"request_id"`
	TimeElapsed string `json:"time_elapsed"`
}

// generateSignature 生成火山引擎API签名
func (c *VolcEngineOCRClient) generateSignature(method, uri, query, body, timestamp string) string {
	// 从API URL解析host
	parsedURL, err := url.Parse(c.config.APIURL)
	if err != nil {
		// 在实际应用中应处理此错误，这里为简化暂不处理
		return ""
	}
	host := parsedURL.Host

	// 计算body的SHA256哈希
	bodyHash := sha256.Sum256([]byte(body))
	bodyHashHex := hex.EncodeToString(bodyHash[:])

	// 构建规范请求
	canonicalRequest := fmt.Sprintf("%s\n%s\n%s\ncontent-type:application/x-www-form-urlencoded\nhost:%s\n\ncontent-type;host\n%s",
		method,
		uri,
		query,
		host,
		bodyHashHex,
	)

	// 计算哈希
	hash := sha256.Sum256([]byte(canonicalRequest))
	canonicalRequestHash := hex.EncodeToString(hash[:])

	// 构建字符串待签名
	shortDate := timestamp[:8] // YYYYMMDD
	credentialScope := fmt.Sprintf("%s/cn-north-1/cv/request", shortDate)
	stringToSign := fmt.Sprintf("HMAC-SHA256\n%s\n%s\n%s",
		timestamp,
		credentialScope,
		canonicalRequestHash)

	// 计算签名
	decodedSecretKey := c.decodeSecretKey(c.config.SecretAccessKey)
	dateKey := c.hmacSHA256([]byte("volc"+decodedSecretKey), shortDate)
	regionKey := c.hmacSHA256(dateKey, "cn-north-1")
	serviceKey := c.hmacSHA256(regionKey, "cv")
	signingKey := c.hmacSHA256(serviceKey, "request")
	signature := hex.EncodeToString(c.hmacSHA256(signingKey, stringToSign))

	return signature
}

// hmacSHA256 计算HMAC-SHA256
func (c *VolcEngineOCRClient) hmacSHA256(key []byte, data string) []byte {
	h := hmac.New(sha256.New, key)
	h.Write([]byte(data))
	return h.Sum(nil)
}

// buildAuthorizationHeader 构建Authorization头
func (c *VolcEngineOCRClient) buildAuthorizationHeader(signature, timestamp string) string {
	shortDate := timestamp[:8]
	credential := fmt.Sprintf("%s/%s/cn-north-1/cv/request", c.config.AccessKeyID, shortDate)
	return fmt.Sprintf("HMAC-SHA256 Credential=%s, SignedHeaders=content-type;host, Signature=%s",
		credential, signature)
}

// ProcessImage 处理图片OCR识别
func (c *VolcEngineOCRClient) ProcessImage(ctx context.Context, imagePath string) (*OCRResult, error) {
	// 读取图片文件
	imageBytes, err := os.ReadFile(imagePath)
	if err != nil {
		return nil, fmt.Errorf("读取图片文件失败: %w", err)
	}

	// Base64编码
	base64Image := base64.StdEncoding.EncodeToString(imageBytes)

	// 构建请求参数
	params := url.Values{}
	params.Set("Action", "RecognizeGeneralText")
	params.Set("Version", "2022-08-31")

	// 构建请求体（form-urlencoded格式）
	bodyParams := url.Values{}
	bodyParams.Set("image_base64", base64Image)
	body := bodyParams.Encode()

	// 生成时间戳
	now := time.Now().UTC()
	timestamp := now.Format("20060102T150405Z")

	// 构建查询字符串
	queryString := params.Encode()

	// 生成签名
	signature := c.generateSignature("POST", "/", queryString, body, timestamp)

	// 构建请求头
	headers := map[string]string{
		"Content-Type":  "application/x-www-form-urlencoded",
		"X-Date":        timestamp,
		"Authorization": c.buildAuthorizationHeader(signature, timestamp),
	}

	// 构建完整URL
	fullURL := c.config.APIURL + "?" + queryString

	// 发送请求
	resp, err := c.client.DoWithRetry("POST", fullURL, []byte(body), headers)
	if err != nil {
		return nil, fmt.Errorf("请求火山引擎OCR API失败: %w", err)
	}
	defer fasthttp.ReleaseResponse(resp)

	// 检查响应状态码
	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("火山引擎OCR API返回错误状态码 %d: %s", resp.StatusCode(), string(resp.Body()))
	}

	// 记录响应内容用于调试
	responseBody := resp.Body()
	fmt.Printf("[火山引擎OCR] 响应内容: %s\n", string(responseBody))

	// 解析响应
	var volcResp VolcEngineOCRResponse
	if err := json.Unmarshal(responseBody, &volcResp); err != nil {
		return nil, fmt.Errorf("解析火山引擎OCR响应失败: %w, 响应内容: %s", err, string(responseBody))
	}

	// 检查业务状态码
	if volcResp.Code != 10000 {
		return nil, fmt.Errorf("火山引擎OCR API返回业务错误，代码: %d, 消息: %s", volcResp.Code, volcResp.Message)
	}

	// 检查是否有识别结果
	if len(volcResp.Data.LineTexts) == 0 {
		return nil, fmt.Errorf("火山引擎OCR未返回识别结果")
	}

	// 转换为统一的OCR结果格式
	result := &OCRResult{
		ImagePath:     imagePath,
		KeyValuePairs: make(map[string]string),
		RawResponse:   resp.Body(),
	}

	// 提取文本和置信度
	var totalConfidence float64
	for i, text := range volcResp.Data.LineTexts {
		// 将文本按索引存储到键值对中
		result.KeyValuePairs[fmt.Sprintf("%d", i)] = text
		// 累加置信度
		if i < len(volcResp.Data.LineProbs) {
			totalConfidence += volcResp.Data.LineProbs[i]
		}
	}

	// 计算平均置信度
	if len(volcResp.Data.LineTexts) > 0 {
		result.Confidence = totalConfidence / float64(len(volcResp.Data.LineTexts))
	}

	// 尝试从文本中提取器官名称（假设第一个文本是器官名称）
	if len(volcResp.Data.LineTexts) > 0 {
		result.OrganName = volcResp.Data.LineTexts[0]
		result.KeyValuePairs["0.000"] = volcResp.Data.LineTexts[0] // 保持与原有格式兼容
	} else {
		result.OrganName = "未知器官"
	}

	fmt.Printf("[火山引擎OCR] 识别完成，置信度: %.2f, 器官名称: %s, 文本数量: %d\n",
		result.Confidence, result.OrganName, len(volcResp.Data.LineTexts))

	return result, nil
}
