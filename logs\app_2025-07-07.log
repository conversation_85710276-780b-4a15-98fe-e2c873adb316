{"level":"INFO","timestamp":"2025-07-07T00:12:03.222+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.222+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.222+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.222+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.222+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.331+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.331+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.331+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":10508}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.333+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.333+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.333+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.333+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.333+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.374+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.374+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.374+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.374+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T00:12:03.374+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.609+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.610+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.610+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.610+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.610+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.610+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.610+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.610+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":1720}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.628+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.629+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.629+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.629+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.629+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.638+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.638+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.638+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.638+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T00:12:09.638+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.921+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.922+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.922+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.922+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.922+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.922+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.922+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.922+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":11432}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.960+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.960+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.960+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.960+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:12.960+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.970+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.971+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.971+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.971+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.971+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.971+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.976+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.978+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.979+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.979+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.979+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.979+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.979+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.979+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.980+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.981+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.982+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.983+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.984+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.985+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.985+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.985+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:13.985+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.726+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.787+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.794+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.794+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.795+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.804+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.806+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.806+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T00:12:14.822+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:17.961+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:17.961+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:17.961+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:22.334+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:22.334+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:12:22.341+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.130+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.130+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.130+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.131+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.131+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.131+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.131+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.131+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:23.131+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T00:12:25.534+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.257+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.291+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.291+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.291+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.291+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.291+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.292+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.292+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.292+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:12:28.292+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:17:13.984+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T00:20:31.982+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:31.987+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:31.989+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:31.992+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:31.997+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.002+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.005+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.006+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.007+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.007+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.011+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.011+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.011+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.011+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.012+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.012+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.012+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.013+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.013+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.015+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.015+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.016+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.016+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.016+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.016+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.017+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.017+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.017+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.024+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.025+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.025+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.025+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.028+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.028+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.028+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.650+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.650+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.650+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.662+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.662+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.662+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.667+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.667+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.667+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.668+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.668+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.668+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.673+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.673+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.673+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.674+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.674+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.674+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.676+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.676+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:32.676+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.306+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.306+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.308+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.314+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.315+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.316+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.316+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.318+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.319+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.339+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.339+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.339+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.340+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.342+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.342+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.345+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.346+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.346+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.346+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.347+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.348+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.503+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.520+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.533+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.550+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.572+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.574+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.601+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.713+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.743+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.751+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.757+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.765+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.803+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:33.858+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.217+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.217+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.217+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.217+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.217+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.217+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.217+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.218+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":22404}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.227+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.227+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.227+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.227+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.227+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.237+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.237+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.237+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.237+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.237+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.350+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.352+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.354+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.355+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.356+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.356+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.356+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.356+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.356+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.357+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.357+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.357+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.362+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.363+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.363+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.545+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.545+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.545+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.558+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.559+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.559+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.559+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.559+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.559+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.743+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.743+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.745+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.792+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.793+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.793+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.794+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.794+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.795+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.974+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:36.976+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.029+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.157+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.181+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.249+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.953+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-07T00:12:12.922+0800"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"11432"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":11432}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":11432}
{"level":"WARN","timestamp":"2025-07-07T00:20:37.954+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":11432,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.955+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":11432}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.955+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.955+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:37.955+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":20784}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.013+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.013+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.013+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.013+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.013+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.443+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.445+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.446+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.446+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.446+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.446+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.454+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.455+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.456+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.456+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.456+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.457+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.457+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.457+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.457+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.457+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.458+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.459+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.460+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.461+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.462+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.463+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.463+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.463+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.463+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.653+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.715+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.721+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.722+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.722+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.730+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.734+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.735+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.753+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.942+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.942+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:38.942+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.150+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.150+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.150+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.150+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.150+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.150+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.151+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.151+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.151+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.184+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.184+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.187+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.226+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.227+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.227+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.227+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.228+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.228+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.228+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.228+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.228+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.393+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:20:39.563+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:38.462+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.156+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.160+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.160+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.160+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.163+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.820+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.820+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:57.820+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:58.472+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:58.472+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:25:58.476+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:58.681+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:25:58.852+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.133+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.138+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.139+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.139+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.145+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.420+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.420+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:29.420+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.546+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.550+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.550+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.550+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.552+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.812+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.812+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:38.812+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.322+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.339+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.339+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.340+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.340+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.340+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.340+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.340+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.340+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.374+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.374+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.375+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.375+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T00:26:41.376+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.618+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":16768}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.620+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.620+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.620+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.620+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.620+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.629+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.629+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.629+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.629+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T00:32:19.629+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.199+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.199+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.199+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.199+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.199+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.199+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.199+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.200+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":16824}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.219+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.219+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.219+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.219+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.219+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.227+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.227+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.227+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.227+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T00:32:26.227+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.529+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.529+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.529+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.529+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.529+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.529+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.530+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.530+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":22340}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.556+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.556+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.556+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.556+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.556+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.958+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.958+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.959+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.959+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.959+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.959+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.965+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.967+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.967+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.967+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.967+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.967+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.968+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.968+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.968+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.968+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.968+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.968+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.969+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.970+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.970+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.970+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.970+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.970+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.971+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.972+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.973+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.974+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.974+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.974+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.974+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.975+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.975+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:28.975+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.537+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.604+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.610+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.611+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.611+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.621+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.624+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.624+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.642+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.717+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.717+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.717+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.717+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.717+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.718+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.718+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.718+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.718+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.798+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.798+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.798+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.798+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.798+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.799+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.799+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.799+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:29.799+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T00:32:30.264+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:32:30.264+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:32:30.264+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:32:30.887+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:32:30.887+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T00:32:30.893+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:32:31.079+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:32:31.290+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:04.056+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:04.056+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:04.056+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:04.056+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:06.314+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:06.314+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:06.314+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:06.314+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:40.650+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:40.650+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:33:40.923+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T00:37:28.974+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T00:42:28.974+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T00:47:28.974+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T00:52:28.974+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T00:57:28.974+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T01:02:28.974+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.707+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.708+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.708+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.708+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.708+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.708+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.708+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.708+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":22356}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.727+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.727+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.727+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.727+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.727+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.748+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.749+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.749+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.749+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.749+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.874+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.876+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.879+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.882+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.882+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.882+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.884+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.884+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.884+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.886+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.886+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.886+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.892+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.893+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:04:58.893+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.233+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.234+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.234+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.234+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.234+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-07T00:32:28.530+0800"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"22340"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":22340}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":22340}
{"level":"WARN","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":22340,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":22340}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.235+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.236+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":21252}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.280+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.281+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.281+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.281+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.281+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.693+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.694+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.695+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.695+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.696+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.696+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.702+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.704+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.705+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.705+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.705+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.705+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.705+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.705+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.705+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.706+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.707+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.708+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.709+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.710+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.711+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.711+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.712+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.713+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.713+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.713+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.915+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.983+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.992+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.992+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:05:02.992+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T01:05:03.002+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T01:05:03.007+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T01:05:03.008+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T01:05:03.025+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.747+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.747+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.747+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.747+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.747+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.747+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.748+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.748+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.748+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.921+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.921+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:05.921+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.030+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.031+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.031+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.031+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.031+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.031+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.031+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.032+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.032+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.669+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.669+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T01:05:08.675+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:11.140+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:05:13.638+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.016+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":19748}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.044+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.044+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.044+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.044+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.044+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.052+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.052+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.052+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.052+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T01:08:34.052+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.804+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.828+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.828+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.829+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.829+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.829+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.829+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.829+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.829+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.843+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.843+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.844+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.844+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T01:08:36.844+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.291+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":22836}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.296+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.296+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.296+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.296+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.296+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.319+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.319+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.319+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.319+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T01:08:40.320+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.524+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.524+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.524+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.524+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.524+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.525+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.525+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.525+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":22200}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.568+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.568+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.568+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.568+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.568+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.994+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.994+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.995+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.995+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.995+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:41.996+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.007+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.009+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.009+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.009+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.009+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.009+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.010+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.010+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.010+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.010+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.010+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.010+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.010+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.011+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.012+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.013+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.014+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.015+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.016+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.016+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.016+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.016+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.016+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.017+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.017+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.625+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.693+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.719+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.719+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.719+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.737+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.788+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.788+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T01:08:42.797+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.132+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.132+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.132+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.132+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.132+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.132+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.133+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.133+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.133+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.699+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.699+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:45.699+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.794+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.794+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.798+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.928+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:48.929+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T01:08:56.840+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:08:59.228+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-06","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:09:01.584+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:09:01.584+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:09:04.056+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:11:02.139+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:11:02.139+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:11:05.063+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.714+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.742+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.742+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.743+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.743+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.743+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.743+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.743+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.743+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.759+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.759+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.759+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.759+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T01:11:17.760+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.653+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.653+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.653+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.653+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.653+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.653+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.653+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.653+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":19936}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.655+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.655+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.655+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.655+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.655+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.664+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.665+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.665+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.665+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T10:01:42.665+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.052+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.052+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.052+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.052+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.052+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.052+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.052+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.052+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":18656}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.077+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.077+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.077+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.077+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.077+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.085+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.085+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.085+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.085+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T10:01:50.086+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:01:53.561+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:01:53.561+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:01:53.561+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:01:53.561+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:53.561+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:01:53.561+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T10:01:53.561+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:53.562+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":18628}
{"level":"INFO","timestamp":"2025-07-07T10:01:53.598+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T10:01:53.598+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T10:01:53.598+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:53.599+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:53.599+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.657+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.658+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.658+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.658+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.658+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.658+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.665+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.667+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.667+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.667+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.668+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.668+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.668+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.668+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.668+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.669+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.669+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.669+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.669+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.669+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.669+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.669+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.669+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.669+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.669+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.670+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.670+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.670+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.670+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.670+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.670+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.670+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.670+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.670+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.670+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.670+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.670+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.671+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.671+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.671+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.671+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.671+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.671+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.671+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.671+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.671+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.671+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.671+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.671+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.672+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.672+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.672+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.672+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.672+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.672+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.672+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.672+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.672+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.672+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.672+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.672+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.673+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.673+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.673+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.673+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.673+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.673+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.673+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.673+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.673+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.673+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.673+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.673+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.673+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.674+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.675+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.675+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:54.675+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.442+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.443+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.443+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.443+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.443+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.443+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.443+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.443+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.443+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.456+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.524+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.527+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.527+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.527+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.529+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.529+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.529+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.529+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.529+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.529+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.532+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.532+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.532+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.539+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.542+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.542+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T10:01:55.559+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:01:56.219+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:01:56.219+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:01:56.219+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:01:56.819+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:01:56.819+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T10:01:56.823+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:01:57.026+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:01:57.253+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:06:54.674+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T10:11:54.674+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T10:14:31.847+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:14:31.847+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:14:31.847+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:14:31.847+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:14:39.434+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:14:39.434+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:14:39.434+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:14:39.434+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:16:54.674+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T10:21:54.674+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T10:22:46.797+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:22:46.797+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:22:47.557+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:23:37.514+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:23:37.514+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:23:37.514+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:23:37.514+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:23:40.918+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-07T10:23:40.964+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T10:23:40.964+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-07T10:23:40.965+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T10:23:40.965+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T10:23:40.965+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T10:23:40.965+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T10:23:40.965+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-07T10:23:40.965+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-07T10:23:40.987+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T10:23:40.987+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T10:23:40.988+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:23:40.988+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T10:23:40.988+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.571+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.571+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.571+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.571+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.571+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.571+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.571+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.571+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":21828}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.593+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.593+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.593+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.593+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.593+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.601+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.601+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.601+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.601+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T10:26:09.602+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.477+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.477+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.477+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.477+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.477+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.477+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.477+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.477+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":24092}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.482+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.482+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.482+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.482+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.482+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.491+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.491+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.491+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.491+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T10:26:15.491+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:26:17.620+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:26:17.620+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:26:17.620+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:26:17.621+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:17.621+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:26:17.621+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T10:26:17.621+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:17.621+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":7960}
{"level":"INFO","timestamp":"2025-07-07T10:26:17.651+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T10:26:17.653+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T10:26:17.653+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:17.653+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:17.654+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.062+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.063+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.063+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.064+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.064+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.064+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.070+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.073+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.073+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.073+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.074+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.074+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.074+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.074+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.074+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.075+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.075+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.075+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.075+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.075+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.075+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.075+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.075+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.075+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.075+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.076+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.076+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.076+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.076+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.076+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.076+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.076+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.076+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.077+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.077+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.077+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.077+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.077+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.077+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.077+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.077+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.077+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.077+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.077+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.077+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.078+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.078+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.078+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.078+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.078+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.078+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.078+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.078+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.079+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.079+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.079+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.079+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.079+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.079+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.079+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.079+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.079+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.079+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.079+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.079+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.079+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.079+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.080+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.080+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.080+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.080+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.080+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.080+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.080+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.080+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.081+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.081+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.081+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.609+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.674+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.681+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.681+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.681+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.690+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.695+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.696+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.708+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.919+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.919+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.920+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.920+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.920+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.920+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.920+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.920+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.920+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.993+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.994+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.994+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.994+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.994+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.994+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.994+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.994+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:18.995+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:26:19.300+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:26:19.300+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:26:19.300+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:26:19.946+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:26:19.946+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T10:26:19.956+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:26:20.184+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:26:20.406+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:26:55.096+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:26:55.096+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:26:55.349+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.213+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.213+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.213+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.213+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.213+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.213+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.213+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.213+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":2344}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.235+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.235+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.235+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.235+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.235+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.263+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.263+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.263+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.263+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.263+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.404+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.408+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.411+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.417+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.417+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.418+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.418+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.420+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.420+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.421+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.421+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.421+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.431+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.432+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:41.433+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.102+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.102+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.102+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.109+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.109+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.109+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.112+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.112+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.112+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.595+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.595+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.595+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.595+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.596+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.596+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":4,"modified":"2025-07-07T10:26:17.621+0800"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.596+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"7960"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.596+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":7960}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.596+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":7960}
{"level":"WARN","timestamp":"2025-07-07T10:29:42.596+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":7960,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.596+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":7960}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.596+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.597+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.597+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":6624}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.634+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.636+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.636+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.636+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:42.636+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.060+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.061+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.062+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.062+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.062+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.062+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.070+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.071+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.072+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.072+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.072+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.072+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.072+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.072+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.072+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.073+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.073+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.073+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.073+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.073+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.073+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.073+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.073+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.073+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.074+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.074+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.074+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.074+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.074+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.074+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.074+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.074+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.074+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.074+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.074+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.075+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.075+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.075+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.075+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.075+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.075+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.075+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.075+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.075+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.075+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.075+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.075+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.075+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.075+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.076+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.076+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.076+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.076+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.076+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.076+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.076+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.076+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.076+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.076+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.076+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.076+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.076+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.076+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.077+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.077+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.077+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.077+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.077+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.077+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.077+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.077+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.077+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.077+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.077+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.078+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.078+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.078+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.078+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.263+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.329+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.338+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.338+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.338+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.345+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.349+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.349+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.357+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.596+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.596+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.596+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.780+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.781+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.781+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.781+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.781+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.781+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.781+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.781+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.781+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.854+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.855+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.855+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.855+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.855+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.855+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.855+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.856+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.856+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.856+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.856+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:29:43.872+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:44.100+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:29:44.309+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:31:23.157+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-07T10:31:23.190+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T10:31:23.190+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-07T10:31:23.191+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T10:31:23.191+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T10:31:23.191+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T10:31:23.191+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T10:31:23.191+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-07T10:31:23.192+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-07T10:31:23.216+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T10:31:23.216+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T10:31:23.217+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:31:23.217+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T10:31:23.217+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.297+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.297+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.297+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.297+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.297+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.297+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.297+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.297+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":15544}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.299+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.299+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.299+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.299+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.299+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.308+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.308+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.308+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.308+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T10:32:03.309+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.210+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.210+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.210+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.210+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.210+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.210+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.210+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.211+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":15592}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.241+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.241+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.241+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.241+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.241+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.663+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.664+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.664+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.664+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.664+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.664+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.673+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.673+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.673+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.673+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.673+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.673+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.673+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.674+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.675+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.675+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.675+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.675+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.794+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.860+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.869+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.869+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.869+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.878+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.878+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.883+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T10:32:16.883+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.377+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.377+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.378+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.378+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.378+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.378+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.378+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.378+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.378+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.468+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.468+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.468+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.469+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.469+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.469+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.469+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.469+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.469+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.556+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.556+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:32:17.556+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:32:18.179+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:32:18.179+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:32:18.179+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:32:18.179+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:32:18.179+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:32:18.179+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T10:32:18.185+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:32:18.368+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:32:18.542+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:32:46.766+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"按钮触发智能截图","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:32:46.766+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:32:46.995+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:36:22.396+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-07T10:36:22.423+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T10:36:22.423+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-07T10:36:22.423+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T10:36:22.423+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T10:36:22.423+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T10:36:22.423+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T10:36:22.423+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-07T10:36:22.424+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-07T10:36:22.443+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T10:36:22.443+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T10:36:22.443+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:36:22.443+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T10:36:22.444+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.035+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.036+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.036+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.036+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.036+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.036+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.036+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.036+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":8968}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.055+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.055+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.055+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.055+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.055+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.065+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.065+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.065+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.065+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T10:41:02.065+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.467+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.467+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.467+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.467+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.467+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.467+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.467+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.468+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":23988}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.506+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.506+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.506+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.506+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.506+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.936+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.936+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.937+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.937+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.937+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.937+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.944+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.944+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.944+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.945+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.946+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.947+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.947+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.947+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:12.947+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.104+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.138+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.162+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.162+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.162+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.173+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.236+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.240+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.240+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.606+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.606+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.606+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.606+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.606+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.606+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.607+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.607+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.607+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.691+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.691+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.691+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.691+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.691+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.691+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.691+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.691+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.691+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.868+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.868+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:41:13.868+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:41:14.574+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:41:14.574+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T10:41:14.581+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:41:14.834+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:41:15.089+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:41:57.022+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:41:57.281+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"王强","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:41:57.780+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:41:58.057+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"王明阳","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:41:59.242+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:41:59.422+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"王强","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:42:01.250+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:42:01.424+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"刘昌军","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:42:02.404+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:42:02.621+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"王明阳","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:46:12.946+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T10:51:12.947+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T10:56:12.946+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.551+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.551+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.551+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.551+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.551+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.551+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.551+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.551+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":15892}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.553+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.553+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.553+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.553+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.553+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.561+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.561+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.561+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.561+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T10:57:49.561+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.434+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.434+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.434+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.434+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.434+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.435+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.435+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.435+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":25772}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.454+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.454+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.454+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.454+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.454+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.463+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.463+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.463+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.463+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T10:58:20.463+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:58:49.768+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:58:49.769+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:58:49.769+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:58:49.769+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:58:49.769+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:58:49.769+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-07T10:41:12.468+0800"}
{"level":"INFO","timestamp":"2025-07-07T10:58:49.769+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"23988"}
{"level":"INFO","timestamp":"2025-07-07T10:58:49.769+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":23988}
{"level":"INFO","timestamp":"2025-07-07T10:58:49.769+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":23988}
{"level":"INFO","timestamp":"2025-07-07T10:58:49.769+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-07T10:58:49.769+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 23988\" /NH"}
{"level":"WARN","timestamp":"2025-07-07T10:58:50.371+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"INFO","timestamp":"2025-07-07T10:58:50.371+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":23988}
{"level":"WARN","timestamp":"2025-07-07T10:58:50.371+0800","caller":"utils/logger.go:101","msg":"删除旧锁文件失败","error":"remove F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The process cannot access the file because it is being used by another process."}
{"level":"INFO","timestamp":"2025-07-07T10:58:50.371+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"ERROR","timestamp":"2025-07-07T10:58:50.371+0800","caller":"utils/logger.go:83","msg":"操作错误","operation":"创建锁文件失败","patient":"","error":"open F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock: The file exists.","stacktrace":"MagneticOperator/app/utils.LogError\n\tF:/myHbuilderAPP/MagneticOperator/app/utils/logger.go:83\nmain.checkSingleInstance\n\tF:/myHbuilderAPP/MagneticOperator/main.go:81\nmain.main\n\tF:/myHbuilderAPP/MagneticOperator/main.go:196\nruntime.main\n\tD:/Program Files/Go/src/runtime/proc.go:283"}
{"level":"INFO","timestamp":"2025-07-07T10:58:50.472+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":23988}
{"level":"INFO","timestamp":"2025-07-07T10:58:50.472+0800","caller":"utils/logger.go:94","msg":"Signal检查失败，尝试tasklist","error":"not supported by windows"}
{"level":"INFO","timestamp":"2025-07-07T10:58:50.472+0800","caller":"utils/logger.go:94","msg":"执行命令","command":"tasklist /FI \"PID eq 23988\" /NH"}
{"level":"WARN","timestamp":"2025-07-07T10:58:51.042+0800","caller":"utils/logger.go:101","msg":"tasklist命令失败","error":"exit status 1"}
{"level":"WARN","timestamp":"2025-07-07T10:58:51.042+0800","caller":"utils/logger.go:101","msg":"单实例检查失败，程序退出"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.581+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.581+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.581+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.581+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.581+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.581+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-07T10:41:12.468+0800"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.581+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"23988"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.581+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":23988}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.581+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":23988}
{"level":"WARN","timestamp":"2025-07-07T10:59:14.581+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":23988,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.581+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":23988}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.582+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.582+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.582+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":24664}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.626+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.626+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.626+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.626+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.626+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.920+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.920+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.921+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.921+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.921+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.921+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.926+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.926+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.926+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.926+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.928+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.928+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.928+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.928+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.928+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.927+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.928+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.928+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.928+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.928+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.928+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.928+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.928+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.928+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:14.928+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:15.042+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T10:59:15.099+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T10:59:15.107+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:59:15.107+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T10:59:15.107+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T10:59:15.118+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T10:59:15.122+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T10:59:15.122+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T10:59:15.130+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:59:19.470+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T10:59:19.470+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T10:59:19.470+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T10:59:19.470+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T10:59:19.470+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:19.470+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:19.470+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:19.470+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:19.470+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T10:59:24.146+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T10:59:24.146+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T10:59:24.146+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T10:59:24.146+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:24.146+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T10:59:24.146+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:24.146+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T10:59:24.146+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:24.146+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T10:59:27.058+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:59:27.058+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:59:27.058+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:59:39.702+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:59:39.702+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T10:59:39.707+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:59:56.456+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:59:59.579+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"王明阳","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T10:59:59.579+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:00:05.428+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"王强","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:00:05.428+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:00:09.529+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:00:13.259+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"王明阳","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:00:13.259+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:00:52.770+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:00:52.935+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:00:53.910+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:00:56.114+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:00:56.789+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:00:57.904+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"王强","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:00:57.904+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:01:02.346+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:01:06.947+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"王明阳","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:01:06.947+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:01:20.597+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:01:27.714+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:01:34.668+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"王强","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:01:34.668+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:01:34.852+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:01:39.523+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:02:15.097+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"王明阳","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:02:15.097+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:02:15.097+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:02:41.686+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:02:49.259+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"王强","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:02:49.259+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:02:56.095+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:03:10.191+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"王明阳","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:03:10.191+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:03:13.661+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:03:17.196+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"王强","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:03:17.196+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:03:23.563+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:03:29.293+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"刘昌军","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:03:29.293+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:03:33.746+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:04:08.332+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"刘昌军","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:04:08.332+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:04:13.622+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:04:14.928+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T11:04:17.832+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"设置当前候检者","patient":"王强","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:04:17.832+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:04:22.697+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
2025-07-07 11:04:22.6974836 +0800 CST m=+308.133952401 write error: write /dev/stdout: The pipe is being closed.
{"level":"INFO","timestamp":"2025-07-07T11:04:22.716+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
2025-07-07 11:04:22.7163752 +0800 CST m=+308.152844001 write error: write /dev/stdout: The pipe is being closed.
{"level":"INFO","timestamp":"2025-07-07T11:04:22.741+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
2025-07-07 11:04:22.7415529 +0800 CST m=+308.178021701 write error: write /dev/stdout: The pipe is being closed.
{"level":"INFO","timestamp":"2025-07-07T11:04:22.759+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已停止"}
2025-07-07 11:04:22.7599603 +0800 CST m=+308.196429101 write error: write /dev/stdout: The pipe is being closed.
{"level":"INFO","timestamp":"2025-07-07T11:04:22.785+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已关闭"}
2025-07-07 11:04:22.7855488 +0800 CST m=+308.222017601 write error: write /dev/stdout: The pipe is being closed.
{"level":"INFO","timestamp":"2025-07-07T11:04:22.803+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
2025-07-07 11:04:22.803922 +0800 CST m=+308.240390801 write error: write /dev/stdout: The pipe is being closed.
{"level":"INFO","timestamp":"2025-07-07T11:04:22.830+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
2025-07-07 11:04:22.8304 +0800 CST m=+308.266868801 write error: write /dev/stdout: The pipe is being closed.
{"level":"INFO","timestamp":"2025-07-07T11:04:22.848+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
2025-07-07 11:04:22.8484105 +0800 CST m=+308.284879301 write error: write /dev/stdout: The pipe is being closed.
{"level":"INFO","timestamp":"2025-07-07T11:04:22.874+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
2025-07-07 11:04:22.8744558 +0800 CST m=+308.310924601 write error: write /dev/stdout: The pipe is being closed.
{"level":"INFO","timestamp":"2025-07-07T11:04:22.911+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
2025-07-07 11:04:22.9119788 +0800 CST m=+308.348447601 write error: write /dev/stdout: The pipe is being closed.
{"level":"INFO","timestamp":"2025-07-07T11:04:22.930+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
2025-07-07 11:04:22.9301222 +0800 CST m=+308.366591001 write error: write /dev/stdout: The pipe is being closed.
{"level":"INFO","timestamp":"2025-07-07T11:04:22.948+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
2025-07-07 11:04:22.9485855 +0800 CST m=+308.385054301 write error: write /dev/stdout: The pipe is being closed.
{"level":"INFO","timestamp":"2025-07-07T11:04:22.974+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
2025-07-07 11:04:22.9746012 +0800 CST m=+308.411070001 write error: write /dev/stdout: The pipe is being closed.
{"level":"INFO","timestamp":"2025-07-07T11:13:58.863+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.863+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.863+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.863+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.863+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.863+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.863+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.864+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":23592}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.884+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.885+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.885+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.885+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.885+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.893+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.893+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.893+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.893+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T11:13:58.893+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:14:12.346+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
2025-07-07 11:14:12.3460414 +0800 CST m=+0.019314901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.358+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
2025-07-07 11:14:12.3588014 +0800 CST m=+0.032074901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.383+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
2025-07-07 11:14:12.3837339 +0800 CST m=+0.057007401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.403+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
2025-07-07 11:14:12.4037087 +0800 CST m=+0.076982201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.427+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
2025-07-07 11:14:12.4273206 +0800 CST m=+0.100594101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.448+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-07T10:59:14.582+0800"}
2025-07-07 11:14:12.4483339 +0800 CST m=+0.121607401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.472+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"24664"}
2025-07-07 11:14:12.4720839 +0800 CST m=+0.145357401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.493+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":24664}
2025-07-07 11:14:12.4932164 +0800 CST m=+0.166489901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.517+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":24664}
2025-07-07 11:14:12.5171652 +0800 CST m=+0.190438701 write error: write /dev/stdout: The handle is invalid.
{"level":"WARN","timestamp":"2025-07-07T11:14:12.537+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":24664,"error":"OpenProcess: The parameter is incorrect."}
2025-07-07 11:14:12.5371909 +0800 CST m=+0.210464401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.561+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":24664}
2025-07-07 11:14:12.5617614 +0800 CST m=+0.235034901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.581+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
2025-07-07 11:14:12.5818526 +0800 CST m=+0.255126101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.598+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
2025-07-07 11:14:12.598221 +0800 CST m=+0.271494501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.626+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":21260}
2025-07-07 11:14:12.6261071 +0800 CST m=+0.299380601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.694+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
2025-07-07 11:14:12.6947347 +0800 CST m=+0.368008201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.715+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
2025-07-07 11:14:12.7152387 +0800 CST m=+0.388512201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.739+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
2025-07-07 11:14:12.7396532 +0800 CST m=+0.412926701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.750+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
2025-07-07 11:14:12.7508198 +0800 CST m=+0.424093301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:12.779+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
2025-07-07 11:14:12.7799 +0800 CST m=+0.453173501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.170+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
2025-07-07 11:14:13.170938 +0800 CST m=+0.844211501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.195+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
2025-07-07 11:14:13.1952601 +0800 CST m=+0.868533601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.215+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
2025-07-07 11:14:13.2158994 +0800 CST m=+0.889172901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.239+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-07 11:14:13.239745 +0800 CST m=+0.913018501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.259+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
2025-07-07 11:14:13.2596364 +0800 CST m=+0.932909901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.283+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
2025-07-07 11:14:13.2837434 +0800 CST m=+0.957016901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.309+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
2025-07-07 11:14:13.3093692 +0800 CST m=+0.982642701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.329+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
2025-07-07 11:14:13.3292566 +0800 CST m=+1.002530101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.348+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
2025-07-07 11:14:13.3488219 +0800 CST m=+1.022095401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.355+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
2025-07-07 11:14:13.3554585 +0800 CST m=+1.028732001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.372+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
2025-07-07 11:14:13.3728702 +0800 CST m=+1.046143701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.396+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
2025-07-07 11:14:13.3969317 +0800 CST m=+1.070205201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.417+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
2025-07-07 11:14:13.4177276 +0800 CST m=+1.091001101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.439+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
2025-07-07 11:14:13.4399366 +0800 CST m=+1.113210101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.462+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
2025-07-07 11:14:13.4620373 +0800 CST m=+1.135310801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.482+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
2025-07-07 11:14:13.4823042 +0800 CST m=+1.155577701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.506+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
2025-07-07 11:14:13.506909 +0800 CST m=+1.180182501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.551+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
2025-07-07 11:14:13.5513793 +0800 CST m=+1.224652801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.571+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
2025-07-07 11:14:13.5713498 +0800 CST m=+1.244623301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.595+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
2025-07-07 11:14:13.5958685 +0800 CST m=+1.269142001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.615+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
2025-07-07 11:14:13.6158245 +0800 CST m=+1.289098001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.640+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
2025-07-07 11:14:13.6404635 +0800 CST m=+1.313737001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.671+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
2025-07-07 11:14:13.6715205 +0800 CST m=+1.344794001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.697+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
2025-07-07 11:14:13.6974116 +0800 CST m=+1.370685101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.727+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
2025-07-07 11:14:13.7270094 +0800 CST m=+1.400282901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.749+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
2025-07-07 11:14:13.7499709 +0800 CST m=+1.423244401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.782+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
2025-07-07 11:14:13.7820054 +0800 CST m=+1.455278901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.808+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
2025-07-07 11:14:13.8084802 +0800 CST m=+1.481753701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.835+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
2025-07-07 11:14:13.8356098 +0800 CST m=+1.508883301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.863+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
2025-07-07 11:14:13.8636827 +0800 CST m=+1.536956201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.894+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
2025-07-07 11:14:13.8940497 +0800 CST m=+1.567323201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.919+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
2025-07-07 11:14:13.9193334 +0800 CST m=+1.592606901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.944+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
2025-07-07 11:14:13.9443464 +0800 CST m=+1.617619901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:13.974+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
2025-07-07 11:14:13.9749553 +0800 CST m=+1.648228801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.005+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
2025-07-07 11:14:14.0050076 +0800 CST m=+1.678281101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.030+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
2025-07-07 11:14:14.0305617 +0800 CST m=+1.703835201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.060+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
2025-07-07 11:14:14.0606265 +0800 CST m=+1.733900001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.086+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
2025-07-07 11:14:14.0864744 +0800 CST m=+1.759747901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.086+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T11:14:14.101+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
2025-07-07 11:14:14.0864744 +0800 CST m=+1.759747901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.101+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
2025-07-07 11:14:14.101056 +0800 CST m=+1.774329501 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:14.101056 +0800 CST m=+1.774329501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.171+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T11:14:14.171+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
2025-07-07 11:14:14.1718117 +0800 CST m=+1.845085201 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:14.1718117 +0800 CST m=+1.845085201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.242+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T11:14:14.242+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
2025-07-07 11:14:14.2427544 +0800 CST m=+1.916027901 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:14.2427544 +0800 CST m=+1.916027901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.273+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T11:14:14.273+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
2025-07-07 11:14:14.2739919 +0800 CST m=+1.947265401 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:14.2739919 +0800 CST m=+1.947265401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.338+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T11:14:14.338+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
2025-07-07 11:14:14.3386627 +0800 CST m=+2.011936201 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:14.3386627 +0800 CST m=+2.011936201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.420+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T11:14:14.420+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
2025-07-07 11:14:14.4200852 +0800 CST m=+2.093358701 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:14.4200852 +0800 CST m=+2.093358701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.449+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T11:14:14.449+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
2025-07-07 11:14:14.4499443 +0800 CST m=+2.123217801 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:14.4499443 +0800 CST m=+2.123217801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.530+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
2025-07-07 11:14:14.5308586 +0800 CST m=+2.204132101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.530+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
2025-07-07 11:14:14.5308586 +0800 CST m=+2.204132101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.555+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T11:14:14.555+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
2025-07-07 11:14:14.5558097 +0800 CST m=+2.229083201 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:14.5558097 +0800 CST m=+2.229083201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.616+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T11:14:14.616+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
2025-07-07 11:14:14.6167257 +0800 CST m=+2.289999201 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:14.6167257 +0800 CST m=+2.289999201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.672+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T11:14:14.672+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
2025-07-07 11:14:14.6722961 +0800 CST m=+2.345569601 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:14.6722961 +0800 CST m=+2.345569601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.728+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T11:14:14.728+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
2025-07-07 11:14:14.7282478 +0800 CST m=+2.401521301 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:14.7282478 +0800 CST m=+2.401521301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.776+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T11:14:14.783+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
2025-07-07 11:14:14.7764357 +0800 CST m=+2.449709201 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:14.7835678 +0800 CST m=+2.456841301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.839+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T11:14:14.839+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
2025-07-07 11:14:14.8394485 +0800 CST m=+2.512722001 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:14.8394485 +0800 CST m=+2.512722001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.895+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T11:14:14.895+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
2025-07-07 11:14:14.8950788 +0800 CST m=+2.568352301 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:14.8950788 +0800 CST m=+2.568352301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:14.946+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T11:14:14.946+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
2025-07-07 11:14:14.9464151 +0800 CST m=+2.619688601 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:14.9464151 +0800 CST m=+2.619688601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:15.006+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T11:14:15.006+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
2025-07-07 11:14:15.0062663 +0800 CST m=+2.679539801 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:15.0062663 +0800 CST m=+2.679539801 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:15.061+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T11:14:15.061+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
2025-07-07 11:14:15.0615458 +0800 CST m=+2.734819301 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:15.0615458 +0800 CST m=+2.734819301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:15.117+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T11:14:15.117+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
2025-07-07 11:14:15.1174671 +0800 CST m=+2.790740601 write error: write /dev/stdout: The handle is invalid.
2025-07-07 11:14:15.1174671 +0800 CST m=+2.790740601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:15.172+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
2025-07-07 11:14:15.1727755 +0800 CST m=+2.846049001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:15.228+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
2025-07-07 11:14:15.2283866 +0800 CST m=+2.901660101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:15.254+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
2025-07-07 11:14:15.2540958 +0800 CST m=+2.927369301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:15.283+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
2025-07-07 11:14:15.2839541 +0800 CST m=+2.957227601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:15.309+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
2025-07-07 11:14:15.3097154 +0800 CST m=+2.982988901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.090+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
2025-07-07 11:14:16.0901076 +0800 CST m=+3.763381101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.109+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
2025-07-07 11:14:16.1097015 +0800 CST m=+3.782975001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.139+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
2025-07-07 11:14:16.1395319 +0800 CST m=+3.812805401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.165+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
2025-07-07 11:14:16.1651434 +0800 CST m=+3.838416901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.195+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
2025-07-07 11:14:16.1952367 +0800 CST m=+3.868510201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.220+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
2025-07-07 11:14:16.2209369 +0800 CST m=+3.894210401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.250+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
2025-07-07 11:14:16.2507574 +0800 CST m=+3.924030901 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.276+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
2025-07-07 11:14:16.2765165 +0800 CST m=+3.949790001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.306+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
2025-07-07 11:14:16.3064259 +0800 CST m=+3.979699401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.405+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
2025-07-07 11:14:16.4056261 +0800 CST m=+4.078899601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.428+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
2025-07-07 11:14:16.4287016 +0800 CST m=+4.101975101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.454+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
2025-07-07 11:14:16.4544008 +0800 CST m=+4.127674301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.484+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
2025-07-07 11:14:16.4843212 +0800 CST m=+4.157594701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.510+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
2025-07-07 11:14:16.5100489 +0800 CST m=+4.183322401 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.539+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
2025-07-07 11:14:16.5399067 +0800 CST m=+4.213180201 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.565+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
2025-07-07 11:14:16.5656581 +0800 CST m=+4.238931601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.595+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
2025-07-07 11:14:16.5955578 +0800 CST m=+4.268831301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:14:16.621+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
2025-07-07 11:14:16.6212201 +0800 CST m=+4.294493601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:16:28.294+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
2025-07-07 11:16:28.2940942 +0800 CST m=+135.967367701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:16:28.807+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
2025-07-07 11:16:28.8072522 +0800 CST m=+136.480525701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:16:28.830+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
2025-07-07 11:16:28.8302786 +0800 CST m=+136.503552101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:16:28.858+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已停止"}
2025-07-07 11:16:28.8583805 +0800 CST m=+136.531654001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:16:28.885+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已关闭"}
2025-07-07 11:16:28.8859555 +0800 CST m=+136.559229001 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:16:28.914+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
2025-07-07 11:16:28.9140052 +0800 CST m=+136.587278701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:16:28.941+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
2025-07-07 11:16:28.9414856 +0800 CST m=+136.614759101 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:16:28.969+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
2025-07-07 11:16:28.9696442 +0800 CST m=+136.642917701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:16:28.997+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
2025-07-07 11:16:28.9971468 +0800 CST m=+136.670420301 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:16:29.039+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
2025-07-07 11:16:29.039552 +0800 CST m=+136.712825501 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:16:29.080+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
2025-07-07 11:16:29.0808442 +0800 CST m=+136.754117701 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:16:29.108+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
2025-07-07 11:16:29.1086171 +0800 CST m=+136.781890601 write error: write /dev/stdout: The handle is invalid.
{"level":"INFO","timestamp":"2025-07-07T11:16:44.344+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.344+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.344+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.344+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.344+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.344+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.344+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.344+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":6600}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.348+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.348+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.348+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.348+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.348+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.358+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.358+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.358+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.358+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T11:16:44.358+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.004+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.005+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.005+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.005+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.005+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.005+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.005+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.005+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":26220}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.016+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.024+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.024+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.024+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.024+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.032+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.032+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.032+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.032+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T11:16:50.033+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.666+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.666+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.666+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.666+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.666+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.667+0800","caller":"utils/logger.go:94","msg":"发现现有锁文件","size":5,"modified":"2025-07-07T11:14:12.650+0800"}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.667+0800","caller":"utils/logger.go:94","msg":"锁文件内容","pid":"21260"}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.667+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":21260}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.667+0800","caller":"utils/logger.go:94","msg":"检查进程是否运行","pid":21260}
{"level":"WARN","timestamp":"2025-07-07T11:16:52.667+0800","caller":"utils/logger.go:101","msg":"查找进程失败","pid":21260,"error":"OpenProcess: The parameter is incorrect."}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.667+0800","caller":"utils/logger.go:94","msg":"进程未找到，清理旧锁文件","pid":21260}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.667+0800","caller":"utils/logger.go:94","msg":"成功删除旧锁文件"}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.667+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.668+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":22388}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.826+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.826+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.826+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.826+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:52.827+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.237+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.238+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.239+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.239+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.239+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.239+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.245+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.248+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.249+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.249+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.249+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.249+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.249+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.249+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.249+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.250+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.250+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.250+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.250+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.250+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.250+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.250+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.250+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.250+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.250+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.250+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.251+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.251+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.251+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.251+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.251+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.251+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.251+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.251+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.252+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.251+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.252+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.252+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.252+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.252+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.252+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.252+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.252+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.252+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.252+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.252+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.252+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.252+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.252+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.253+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.253+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.253+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.253+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.253+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.253+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.253+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.253+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.254+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.254+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.254+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.254+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.254+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.254+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.254+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.254+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.254+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.254+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.254+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.254+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.255+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.255+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.255+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.255+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.255+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.256+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.256+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.256+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.256+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.773+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.840+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.847+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.847+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.847+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.855+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.859+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.859+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.872+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.988+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.989+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.989+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.989+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.989+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.989+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.989+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.989+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:53.989+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T11:16:54.090+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T11:16:54.090+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T11:16:54.090+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T11:16:54.090+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:54.091+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T11:16:54.091+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:54.091+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T11:16:54.091+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:54.091+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:16:54.474+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:16:54.474+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:16:54.474+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:16:55.139+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:16:55.139+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T11:16:55.150+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:16:55.367+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:16:55.588+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:21:53.256+0800","caller":"utils/logger.go:94","msg":"清理已完成任务","remaining":0}
{"level":"INFO","timestamp":"2025-07-07T11:25:37.055+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T11:25:37.059+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T11:25:37.059+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T11:25:37.059+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T11:25:37.062+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:25:37.728+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:25:37.728+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:25:37.728+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:25:38.431+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:25:38.431+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T11:25:38.434+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:25:38.678+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:25:38.912+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:25:53.967+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T11:25:53.971+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T11:25:53.971+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T11:25:53.971+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T11:25:53.973+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:25:54.180+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:25:54.180+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:25:54.180+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:25:54.419+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:25:54.419+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T11:25:54.424+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:25:54.647+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:25:54.885+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:26:39.884+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-07T11:26:39.921+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"停止快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T11:26:39.922+0800","caller":"utils/logger.go:94","msg":"快捷键服务已停止"}
{"level":"INFO","timestamp":"2025-07-07T11:26:39.922+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T11:26:39.923+0800","caller":"utils/logger.go:94","msg":"失败任务管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T11:26:39.923+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已停止"}
{"level":"INFO","timestamp":"2025-07-07T11:26:39.923+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已关闭"}
{"level":"INFO","timestamp":"2025-07-07T11:26:39.923+0800","caller":"utils/logger.go:94","msg":"OCR服务已关闭"}
{"level":"INFO","timestamp":"2025-07-07T11:26:39.923+0800","caller":"utils/logger.go:94","msg":"应用清理工作完成"}
{"level":"INFO","timestamp":"2025-07-07T11:26:39.964+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T11:26:39.964+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T11:26:39.965+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:26:39.965+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T11:26:39.966+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:27:09.993+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T11:27:09.993+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T11:27:09.993+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T11:27:09.993+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:09.994+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:27:09.994+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T11:27:09.994+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:09.994+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":6920}
{"level":"INFO","timestamp":"2025-07-07T11:27:09.998+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T11:27:09.998+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T11:27:09.998+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:09.998+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:09.998+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:10.006+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T11:27:10.006+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T11:27:10.006+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:10.006+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T11:27:10.006+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.808+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.808+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.808+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.808+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.808+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.808+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.808+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.809+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":25616}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.810+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.810+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.810+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.810+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.810+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.819+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.819+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.819+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.819+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T11:27:16.819+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.251+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.252+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.252+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.252+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.252+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.252+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.252+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.253+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":1920}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.299+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.299+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.299+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.299+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.300+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.717+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.718+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.718+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.718+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.718+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.719+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.726+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.730+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.730+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.730+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.730+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.731+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.731+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.731+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.731+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.731+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.731+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.731+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.732+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.732+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.732+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.732+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.732+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.732+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.732+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.732+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.732+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.732+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.732+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.733+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.733+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.733+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.733+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.733+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.733+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.734+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.734+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.734+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.734+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.734+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.734+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.734+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.734+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.734+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.734+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.735+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.735+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.735+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.735+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.735+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.735+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.735+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.735+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.735+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.735+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.736+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.736+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.736+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.736+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.736+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.736+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.736+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.736+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.736+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.736+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.736+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.736+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.737+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.737+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.737+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.737+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:19.737+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.312+0800","caller":"utils/logger.go:94","msg":"GetConfig called - SiteInfo: 北京市通州区潞城镇潞城社区卫生服务中心 (YL-BJ-TZ-001)"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.374+0800","caller":"utils/logger.go:94","msg":"DOM准备就绪，开始设置窗口位置和尺寸"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.382+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo called - 开始获取站点信息"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.382+0800","caller":"utils/logger.go:94","msg":"使用缓存的站点信息"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.382+0800","caller":"utils/logger.go:94","msg":"GetSiteInfo - 成功获取站点信息: ID=YL-BJ-TZ-001, Name=北京市通州区潞城镇潞城社区卫生服务中心"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.391+0800","caller":"utils/logger.go:94","msg":"窗口位置设置为紧凑模式: x=2944, y=748, width=512, height=806"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.397+0800","caller":"utils/logger.go:94","msg":"窗体已设置为置顶"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.397+0800","caller":"utils/logger.go:94","msg":"窗体已显示"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.406+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.409+0800","caller":"utils/logger.go:94","msg":"站点信息无变化，复用现有二维码"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.409+0800","caller":"utils/logger.go:94","msg":"initServices() 完成"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.410+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已就绪"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.410+0800","caller":"utils/logger.go:94","msg":"窗体状态初始化完成"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.410+0800","caller":"utils/logger.go:94","msg":"开始创建必要的目录..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.410+0800","caller":"utils/logger.go:94","msg":"pic目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.410+0800","caller":"utils/logger.go:94","msg":"config目录创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.410+0800","caller":"utils/logger.go:94","msg":"开始检测OCR环境..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.410+0800","caller":"utils/logger.go:94","msg":"正在测试网络连接"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.503+0800","caller":"utils/logger.go:94","msg":"网络连接测试成功: https://www.baidu.com, 状态码: 200"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.503+0800","caller":"utils/logger.go:94","msg":"检测到火山引擎OCR配置，OCR服务可用"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.503+0800","caller":"utils/logger.go:94","msg":"OCR环境检测通过"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.503+0800","caller":"utils/logger.go:94","msg":"开始启动快捷键监听..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.503+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"启动快捷键监听","patient":"","site_id":""}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.504+0800","caller":"utils/logger.go:94","msg":"快捷键服务启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.504+0800","caller":"utils/logger.go:94","msg":"启动OCR任务清理定时器..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.504+0800","caller":"utils/logger.go:94","msg":"OCR任务清理定时器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:20.504+0800","caller":"utils/logger.go:94","msg":"应用启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:27:21.159+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:27:21.159+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:27:21.159+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取未分析患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:27:21.417+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"生成报到二维码","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:27:21.418+0800","caller":"utils/logger.go:94","msg":"复用了缓存的报到二维码"}
{"level":"INFO","timestamp":"2025-07-07T11:27:21.429+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:27:21.663+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取待检测候检者列表","patient":"","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:27:21.871+0800","caller":"utils/logger.go:72","msg":"操作记录","operation":"获取指定日期已完成患者列表","patient":"2025-07-07","site_id":"YL-BJ-TZ-001"}
{"level":"INFO","timestamp":"2025-07-07T11:27:56.772+0800","caller":"utils/logger.go:94","msg":"收到退出信号，开始清理..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:56.772+0800","caller":"utils/logger.go:94","msg":"应用开始关闭，执行清理工作..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:56.772+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:27:56.772+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T11:27:56.773+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:27:56.773+0800","caller":"utils/logger.go:94","msg":"清理完成，程序退出"}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.396+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.397+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.397+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.397+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.397+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.397+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.397+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.397+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":24264}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.414+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.416+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.416+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.416+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.416+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.425+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.425+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.425+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.425+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T11:32:36.425+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.058+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.058+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.058+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.058+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.058+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.058+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.058+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.058+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":376}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.069+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.070+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.070+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.070+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.070+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.079+0800","caller":"utils/logger.go:94","msg":"Wails.Run()调用完成"}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.079+0800","caller":"utils/logger.go:94","msg":"Wails应用正常退出"}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.079+0800","caller":"utils/logger.go:94","msg":"清理锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.079+0800","caller":"utils/logger.go:94","msg":"关闭锁文件句柄"}
{"level":"INFO","timestamp":"2025-07-07T11:32:43.080+0800","caller":"utils/logger.go:94","msg":"成功删除锁文件","path":"C:\\Users\\<USER>\\AppData\\Local\\Temp\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.496+0800","caller":"utils/logger.go:94","msg":"应用程序启动","version":"1.0.0"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.497+0800","caller":"utils/logger.go:94","msg":"启动磁感分析操作台"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.497+0800","caller":"utils/logger.go:94","msg":"开始单实例检查"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.497+0800","caller":"utils/logger.go:94","msg":"检查现有实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.497+0800","caller":"utils/logger.go:94","msg":"锁文件路径","path":"F:\\myHbuilderAPP\\MagneticOperator\\build\\bin\\MagneticOperator.lock"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.497+0800","caller":"utils/logger.go:94","msg":"未找到现有锁文件"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.497+0800","caller":"utils/logger.go:94","msg":"尝试创建新锁文件..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.497+0800","caller":"utils/logger.go:94","msg":"写入当前PID到锁文件","pid":23904}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.535+0800","caller":"utils/logger.go:94","msg":"成功创建锁文件，允许启动"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.536+0800","caller":"utils/logger.go:94","msg":"单实例检查通过"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.536+0800","caller":"utils/logger.go:94","msg":"创建App实例..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.536+0800","caller":"utils/logger.go:94","msg":"App实例创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.536+0800","caller":"utils/logger.go:94","msg":"开始启动Wails应用..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.949+0800","caller":"utils/logger.go:94","msg":"=== STARTUP函数被调用 ==="}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.949+0800","caller":"utils/logger.go:94","msg":"日志系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.950+0800","caller":"utils/logger.go:94","msg":"消息配置系统初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.950+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.950+0800","caller":"utils/logger.go:94","msg":"开始调用 initServices()..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.950+0800","caller":"utils/logger.go:94","msg":"开始初始化服务..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.956+0800","caller":"utils/logger.go:94","msg":"成功加载配置文件"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.960+0800","caller":"utils/logger.go:94","msg":"已启用串行OCR处理模式，确保API请求按顺序执行"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.960+0800","caller":"utils/logger.go:94","msg":"加载了0个失败任务"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.960+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.961+0800","caller":"utils/logger.go:94","msg":"失败任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.961+0800","caller":"utils/logger.go:94","msg":"简化截图管理器已启动","user":"default_user"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.961+0800","caller":"utils/logger.go:94","msg":"简化截图管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.961+0800","caller":"utils/logger.go:94","msg":"简化截图API创建成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.961+0800","caller":"utils/logger.go:94","msg":"开始初始化任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.962+0800","caller":"utils/logger.go:94","msg":"开始创建任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.962+0800","caller":"utils/logger.go:94","msg":"截图任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.962+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.962+0800","caller":"utils/logger.go:94","msg":"上传任务处理器创建完成"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.962+0800","caller":"utils/logger.go:94","msg":"开始注册任务处理器..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.962+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_A"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.962+0800","caller":"utils/logger.go:94","msg":"截图A任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.962+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_B"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.962+0800","caller":"utils/logger.go:94","msg":"截图B任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.963+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"screenshot_C"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.963+0800","caller":"utils/logger.go:94","msg":"截图C任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.963+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"ocr_process"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.963+0800","caller":"utils/logger.go:94","msg":"OCR任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.963+0800","caller":"utils/logger.go:94","msg":"任务处理器注册成功","taskType":"upload"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.963+0800","caller":"utils/logger.go:94","msg":"上传任务处理器注册成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.963+0800","caller":"utils/logger.go:94","msg":"开始启动任务管理器..."}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.963+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功","maxConcurrent":20,"registeredHandlers":5,"cooldownPeriod":0.1}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.963+0800","caller":"utils/logger.go:94","msg":"启动工作协程","workerCount":20}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.964+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":0}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.964+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":1}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.964+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":0}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.964+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":2}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.964+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":1}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.964+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":2}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.964+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":3}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.964+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":3}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.964+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":4}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.964+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":4}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.964+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":5}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.964+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":5}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.964+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":6}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.965+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":6}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.965+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":7}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.965+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":7}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.965+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":8}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.965+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":8}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.965+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":9}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.965+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":9}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.965+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":10}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.965+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":11}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.965+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":10}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.965+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":11}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.966+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":12}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.967+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":13}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.967+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":14}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.967+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":15}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.966+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":12}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.967+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":13}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.967+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":14}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.967+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":15}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.967+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":16}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.967+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":16}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.968+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":17}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.968+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":18}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.968+0800","caller":"utils/logger.go:94","msg":"工作协程启动","workerId":19}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.968+0800","caller":"utils/logger.go:94","msg":"清理协程启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.970+0800","caller":"utils/logger.go:94","msg":"任务管理器启动事件已发送"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.968+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":17}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.968+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":18}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.968+0800","caller":"utils/logger.go:94","msg":"任务工作协程启动","workerID":19}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.972+0800","caller":"utils/logger.go:94","msg":"任务管理器启动成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.973+0800","caller":"utils/logger.go:94","msg":"任务管理器初始化成功"}
{"level":"INFO","timestamp":"2025-07-07T11:32:45.973+0800","caller":"utils/logger.go:94","msg":"开始从API加载站点信息..."}
