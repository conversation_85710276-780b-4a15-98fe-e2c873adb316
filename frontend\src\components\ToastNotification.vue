<template>
  <div class="toast-container">
    <!-- Toast通知列表 -->
    <transition-group name="toast" tag="div" class="toast-list">
      <div
        v-for="toast in toasts"
        :key="toast.id"
        :class="[
          'toast',
          `toast-${toast.type}`,
          { 'toast-with-progress': toast.showProgress }
        ]"
      >
        <!-- 图标 -->
        <ToastIcon :type="toast.type" />

        <!-- 内容 -->
        <div class="toast-content">
          <div class="toast-title">{{ toast.title }}</div>
          <div class="toast-message">{{ toast.message }}</div>
          
          <!-- 进度条 -->
          <div v-if="toast.showProgress" class="toast-progress">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: toast.progress + '%' }"
              ></div>
            </div>
            <span class="progress-text">{{ toast.progress }}%</span>
          </div>
        </div>

        <!-- 关闭按钮 -->
        <button 
          v-if="toast.closable !== false" 
          @click="removeToast(toast.id)"
          class="toast-close"
          :aria-label="`关闭${toast.title}通知`"
        >
          <CloseIcon />
        </button>

        <!-- 自动消失进度条 -->
        <div 
          v-if="toast.duration > 0" 
          class="toast-timer"
          :style="{ animationDuration: toast.duration + 'ms' }"
        ></div>
      </div>
    </transition-group>

    <!-- 调试面板已彻底移除 -->
  </div>
</template>

<script>
import { h } from 'vue'
import { useWailsToastRuntime } from '../composables/useWailsRuntime.js'
import TOAST_CONFIG from '../config/toastConfig.js'
import ToastIcon from './ToastIcon.vue'

// 关闭图标组件
const CloseIcon = {
  render() {
    return h('svg', {
      viewBox: '0 0 14 14',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      h('path', {
        d: 'M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7 14 1.41z',
        fill: 'currentColor'
      })
    ])
  }
}

export default {
  name: 'ToastNotification',
  components: {
    ToastIcon,
    CloseIcon
  },
  props: {
    config: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      toasts: [],
      toastQueue: [],
      nextId: 1,
      wailsRuntimeStatus: 'checking',
      eventListenerStatus: 'not-set',
      eventListenerCleanup: null,
      retryTimeouts: [],
      // taskManagerStatus相关代码已移除
    }
  },
  computed: {
    mergedConfig() {
      return { ...TOAST_CONFIG, ...this.config }
    },
    isDevelopment() {
      return process.env.NODE_ENV === 'development'
    }
  },
  mounted() {
    this.log('组件已挂载')
    this.setupEventListener()
    this.processQueue()
    // 任务状态更新功能已移除
  },
  beforeUnmount() {
    this.cleanup()
    // 任务状态更新功能已移除
  },
  methods: {
    // 日志记录
    log(message, data = null) {
      if (this.mergedConfig.debug || this.isDevelopment) {
        console.log(`[ToastNotification] ${message}`, data || '')
      }
    },

    // 错误记录
    error(message, error = null) {
      console.error(`[ToastNotification] ${message}`, error || '')
    },

    // 设置事件监听器（带重试机制和错误处理）
    setupEventListener(retryCount = 0) {
      const maxRetries = this.mergedConfig.retryAttempts
      const retryDelay = this.mergedConfig.retryDelay
      
      try {
        if (window.runtime && window.runtime.EventsOn) {
          this.wailsRuntimeStatus = 'available'
          this.log('Wails runtime 可用，设置 showToastNotification 事件监听器')
          
          // 设置事件监听器
          window.runtime.EventsOn('showToastNotification', this.handleBackendNotification)
          this.eventListenerStatus = 'active'
          this.log('事件监听器设置完成')
          
          // 清理重试定时器
          this.clearRetryTimeouts()
          
        } else if (retryCount < maxRetries) {
          this.wailsRuntimeStatus = 'retrying'
          this.log(`Wails runtime 尚未可用，${retryDelay}ms后重试 (${retryCount + 1}/${maxRetries})`)
          
          const timeoutId = setTimeout(() => {
            this.setupEventListener(retryCount + 1)
          }, retryDelay)
          
          this.retryTimeouts.push(timeoutId)
          
        } else {
          this.wailsRuntimeStatus = 'failed'
          this.eventListenerStatus = 'failed'
          this.error('Wails runtime 在最大重试次数后仍不可用！')
          
          // 降级处理：显示错误通知
          this.showToast({
            title: '系统警告',
            message: 'Toast通知系统初始化失败，部分功能可能受限',
            type: 'warning',
            duration: 8000
          })
        }
      } catch (err) {
        this.error('设置事件监听器时发生错误:', err)
        this.wailsRuntimeStatus = 'error'
        this.eventListenerStatus = 'error'
      }
    },

    // 清理重试定时器
    clearRetryTimeouts() {
      this.retryTimeouts.forEach(timeoutId => clearTimeout(timeoutId))
      this.retryTimeouts = []
    },

    // 处理后端发送的通知
    handleBackendNotification(data) {
      try {
        this.log('收到后端通知事件:', data)
        
        if (!data || typeof data !== 'object') {
          this.error('收到无效的后端通知数据:', data)
          return
        }
        
        const { title, message, type, duration, showProgress, progress } = data
        this.log('解析后的数据:', { title, message, type, duration, showProgress, progress })
        
        this.showToast({
          title: title || '通知',
          message: message || '',
          type: type || 'info',
          duration: duration !== undefined ? duration : this.mergedConfig.defaultDuration,
          showProgress: showProgress || false,
          progress: progress || 0
        })
      } catch (err) {
        this.error('处理后端通知时发生错误:', err)
      }
    },

    // 显示Toast通知（带队列管理）
    showToast(options) {
      try {
        console.log('[ToastNotification] showToast被调用，参数:', options)
        console.log('[ToastNotification] 当前mergedConfig:', this.mergedConfig)
        console.log('[ToastNotification] 当前toasts数组长度:', this.toasts.length)
        
        this.log('showToast 被调用，参数:', options)
        
        if (!options || typeof options !== 'object') {
          this.error('showToast 收到无效参数:', options)
          return null
        }
        
        const toast = {
          id: this.nextId++,
          title: options.title || '通知',
          message: options.message || '',
          type: options.type || 'info',
          duration: options.duration !== undefined ? options.duration : this.mergedConfig.defaultDuration,
          showProgress: options.showProgress || false,
          progress: options.progress || 0,
          closable: options.closable !== false,
          timestamp: Date.now()
        }

        console.log('[ToastNotification] 创建的Toast对象:', toast)
        this.log('创建的Toast对象:', toast)
        
        // 队列管理
        if (this.mergedConfig.enableQueue && this.toasts.length >= this.mergedConfig.maxToasts) {
          console.log('[ToastNotification] 达到最大显示数量，添加到队列')
          this.toastQueue.push(toast)
          this.log('Toast已加入队列，当前队列长度:', this.toastQueue.length)
        } else {
          console.log('[ToastNotification] 未达到最大显示数量，直接显示')
          this.addToastToDisplay(toast)
        }
        
        console.log('[ToastNotification] showToast执行完成，返回toast.id')
        return toast.id
      } catch (err) {
        this.error('显示Toast时发生错误:', err)
        return null
      }
    },

    // 添加Toast到显示列表
    addToastToDisplay(toast) {
      console.log('[ToastNotification] addToastToDisplay被调用，toast:', toast)
      console.log('[ToastNotification] 添加前toasts数组长度:', this.toasts.length)
      
      this.toasts.push(toast)
      
      console.log('[ToastNotification] 添加后toasts数组长度:', this.toasts.length)
      console.log('[ToastNotification] 添加后toasts数组内容:', this.toasts)
      
      this.log('当前Toast列表:', this.toasts)
      
      // 强制触发Vue响应性更新
      this.$forceUpdate()
      
      // 向父组件发送通知事件
      this.$emit('notification-shown', {
        id: toast.id,
        message: toast.message,
        type: toast.type,
        timestamp: toast.timestamp
      })

      // 自动移除
      if (toast.duration > 0) {
        setTimeout(() => {
          this.removeToast(toast.id)
        }, toast.duration)
      }
    },

    // 处理队列
    processQueue() {
      if (this.toastQueue.length > 0 && this.toasts.length < this.mergedConfig.maxToasts) {
        const nextToast = this.toastQueue.shift()
        this.addToastToDisplay(nextToast)
        
        // 继续处理队列
        setTimeout(() => this.processQueue(), 100)
      }
    },

    // 更新Toast进度
    updateToastProgress(id, progress, message) {
      try {
        const toast = this.toasts.find(t => t.id === id)
        if (toast) {
          toast.progress = Math.min(100, Math.max(0, progress))
          if (message !== undefined) {
            toast.message = message
          }
          this.log(`更新Toast ${id} 进度:`, { progress: toast.progress, message })
        } else {
          this.log(`未找到ID为 ${id} 的Toast`)
        }
      } catch (err) {
        this.error('更新Toast进度时发生错误:', err)
      }
    },

    // 移除Toast
    removeToast(id) {
      try {
        const index = this.toasts.findIndex(t => t.id === id)
        if (index > -1) {
          const removedToast = this.toasts.splice(index, 1)[0]
          this.log('移除Toast:', removedToast)
          
          // 向父组件发送移除事件
          this.$emit('notification-removed', {
            id: removedToast.id,
            type: removedToast.type
          })
          
          // 处理队列中的下一个Toast
          this.processQueue()
        }
      } catch (err) {
        this.error('移除Toast时发生错误:', err)
      }
    },

    // 清除所有Toast
    clearAllToasts() {
      try {
        const clearedCount = this.toasts.length
        this.toasts = []
        this.toastQueue = []
        this.log(`清除了 ${clearedCount} 个Toast和队列`)
        
        this.$emit('all-notifications-cleared')
      } catch (err) {
        this.error('清除所有Toast时发生错误:', err)
      }
    },

    // 便捷方法
    showSuccess(title, message, duration) {
      return this.showToast({ title, message, type: 'success', duration })
    },

    showError(title, message, duration) {
      return this.showToast({ title, message, type: 'error', duration })
    },

    showWarning(title, message, duration) {
      return this.showToast({ title, message, type: 'warning', duration })
    },

    showInfo(title, message, duration) {
      return this.showToast({ title, message, type: 'info', duration })
    },

    showProgress(title, message, initialProgress = 0) {
      return this.showToast({
        title,
        message,
        type: 'info',
        duration: 0,
        showProgress: true,
        progress: initialProgress
      })
    },

    // 健康检查
    healthCheck() {
      return {
        wailsRuntimeStatus: this.wailsRuntimeStatus,
        eventListenerStatus: this.eventListenerStatus,
        activeToasts: this.toasts.length,
        queuedToasts: this.toastQueue.length,
        config: this.mergedConfig
      }
    },

    // 清理资源
    cleanup() {
      try {
        this.log('开始清理资源')
        
        // 清理重试定时器
        this.clearRetryTimeouts()
        
        // 清理事件监听器
        if (this.eventListenerCleanup) {
          this.eventListenerCleanup()
        }
        
        // 清理Toast
        this.toasts = []
        this.toastQueue = []
        
        this.log('资源清理完成')
      } catch (err) {
        this.error('清理资源时发生错误:', err)
      }
    },

    // 任务状态更新相关方法已移除

    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return 'N/A'
      try {
        const date = new Date(timestamp)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      } catch (err) {
        return 'Invalid Date'
      }
    }
  }
}
</script>

<style scoped>
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 999999;
  pointer-events: none;
  width: auto;
  height: auto;
  max-width: 400px;
}

.toast-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.toast {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  min-width: 320px;
  max-width: 380px;
  width: 100%;
  box-sizing: border-box;
  padding: 16px;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2), 0 4px 16px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  pointer-events: auto;
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
  will-change: transform, opacity;
}

.toast-success {
  border-left: 4px solid #10b981;
}

.toast-success .toast-icon {
  color: #10b981;
}

.toast-error {
  border-left: 4px solid #ef4444;
}

.toast-error .toast-icon {
  color: #ef4444;
}

.toast-warning {
  border-left: 4px solid #f59e0b;
}

.toast-warning .toast-icon {
  color: #f59e0b;
}

.toast-info {
  border-left: 4px solid #3b82f6;
}

.toast-info .toast-icon {
  color: #3b82f6;
}

.toast-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.toast-content {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
  margin-bottom: 4px;
  line-height: 1.4;
}

.toast-message {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.5;
  word-wrap: break-word;
}

.toast-progress {
  margin-top: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
  min-width: 32px;
  text-align: right;
}

.toast-close {
  flex-shrink: 0;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  margin-top: -2px;
  transition: all 0.2s ease;
}

.toast-close:hover {
  color: #6b7280;
  background: rgba(0, 0, 0, 0.05);
}

.toast-close:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.toast-timer {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  animation: toast-timer linear;
  animation-fill-mode: forwards;
}

@keyframes toast-timer {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* 动画效果 */
.toast-enter-active {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.toast-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%) scale(0.8);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%) scale(0.9);
}

.toast-move {
  transition: transform 0.3s ease;
}

/* 调试面板相关样式已移除 */

.active-tasks-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.3);
}

.task-item {
  padding: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 10px;
}

.task-item:last-child {
  border-bottom: none;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  flex-wrap: wrap;
  gap: 4px;
}

.task-id {
  color: #60a5fa;
  font-weight: bold;
}

.task-type {
  color: #34d399;
  background: rgba(52, 211, 153, 0.2);
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 9px;
}

.task-status {
  color: #fbbf24;
  background: rgba(251, 191, 36, 0.2);
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 9px;
}

.task-details p {
  margin: 2px 0;
  color: #d1d5db;
}

.task-details strong {
  color: #f3f4f6;
}

.no-tasks {
  text-align: center;
  color: #9ca3af;
  font-style: italic;
  padding: 12px;
}

/* 滚动条样式 */
.active-tasks-container::-webkit-scrollbar {
  width: 6px;
}

.active-tasks-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.active-tasks-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.active-tasks-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
  }
  
  .toast {
    min-width: auto;
    max-width: none;
  }
  
  /* debug-panel样式已移除 */
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .toast {
    background: rgba(31, 41, 55, 0.95);
    border: 1px solid rgba(75, 85, 99, 0.3);
  }
  
  .toast-title {
    color: #f9fafb;
  }
  
  .toast-message {
    color: #d1d5db;
  }
  
  .toast-close {
    color: #9ca3af;
  }
  
  .toast-close:hover {
    color: #d1d5db;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .progress-bar {
    background: #374151;
  }
}
</style>