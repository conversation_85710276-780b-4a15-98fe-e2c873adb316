package services

import (
	"context"
	"encoding/base64"
	"fmt"
	"net/url"
	"os"

	json "github.com/goccy/go-json"
	"github.com/volcengine/volc-sdk-golang/service/visual"

	"MagneticOperator/app/models"
)

// VolcEngineSDKOCRClient 使用官方SDK的火山引擎OCR客户端
type VolcEngineSDKOCRClient struct {
	config *models.VolcEngineOCRConfig
}

// NewVolcEngineSDKOCRClient 创建使用官方SDK的火山引擎OCR客户端
func NewVolcEngineSDKOCRClient(config *models.VolcEngineOCRConfig) *VolcEngineSDKOCRClient {
	return &VolcEngineSDKOCRClient{
		config: config,
	}
}

// decodeSecretKey 解码Base64编码的密钥
func (c *VolcEngineSDKOCRClient) decodeSecretKey(encodedKey string) string {
	// 尝试Base64解码
	if decoded, err := base64.StdEncoding.DecodeString(encodedKey); err == nil {
		decodedStr := string(decoded)
		// 检查是否还是Base64编码（双重编码的情况）
		// 先尝试添加填充字符（如果需要）
		paddedStr := decodedStr
		if len(paddedStr)%4 != 0 {
			paddedStr += "="
		}

		if decoded2, err2 := base64.StdEncoding.DecodeString(paddedStr); err2 == nil {
			// 双重Base64编码，返回最终解码结果
			finalKey := string(decoded2)
			fmt.Printf("[火山引擎OCR SDK] 检测到双重Base64编码的密钥，已解码: %s\n", finalKey)
			return finalKey
		}
		// 单次Base64编码
		fmt.Printf("[火山引擎OCR SDK] 检测到Base64编码的密钥，已解码\n")
		return decodedStr
	}
	// 不是Base64编码，直接返回原值
	return encodedKey
}

// ProcessImage 使用官方SDK处理图片
func (c *VolcEngineSDKOCRClient) ProcessImage(ctx context.Context, imagePath string) (*OCRResult, error) {
	fmt.Printf("[火山引擎OCR SDK] 开始处理图片: %s\n", imagePath)

	// 配置SDK凭证
	visual.DefaultInstance.Client.SetAccessKey(c.config.AccessKeyID)
	// 解码SecretAccessKey（处理Base64编码的情况）
	decodedSecretKey := c.decodeSecretKey(c.config.SecretAccessKey)
	visual.DefaultInstance.Client.SetSecretKey(decodedSecretKey)

	fmt.Printf("[火山引擎OCR SDK] 使用AccessKeyID: %s\n", c.config.AccessKeyID)
	fmt.Printf("[火山引擎OCR SDK] SecretAccessKey长度: %d\n", len(decodedSecretKey))

	// 设置区域（火山引擎OCR服务在cn-north-1区域）
	visual.DefaultInstance.SetRegion("cn-north-1")
	fmt.Printf("[火山引擎OCR SDK] 设置区域: cn-north-1\n")

	// 读取图片文件
	imageBytes, err := os.ReadFile(imagePath)
	if err != nil {
		return nil, fmt.Errorf("读取图片文件失败: %w", err)
	}

	// Base64编码
	base64Image := base64.StdEncoding.EncodeToString(imageBytes)

	// 构建请求参数
	form := url.Values{}
	form.Add("image_base64", base64Image)

	// 使用通用OCR
	// https://github.com/volcengine/volc-sdk-golang/blob/main/example/visual/ocr_demo.go
	// action = "OCRNormal"          // 通用OCR
	// action = "MultiLanguageOCR"   // 多语种OCR
	// action = "BankCard"           // 银行卡识别，有v1,v2版本
	// action = "IDCard"             // 身份证识别，有v1,v2版本
	// action = "DrivingLicense"     // 驾驶证识别
	// action = "VehicleLicense"     // 行驶证识别
	// action = "OcrTaibao"          // 台湾居民来往大陆通行证识别
	// action = "OcrVatInvoice"      // 增值税发票识别
	// action = "OcrTaxiInvoice"     // 出租车发票识别
	// action = "OcrQuotaInvoice"    // 定额发票识别
	// action = "OcrTrainTicket"     // 火车票识别
	// action = "OcrFlightInvoice"   // 飞机行程单识别
	// action = "OcrFinance"         // 混贴报销场景
	// action = "OcrRollInvoice"     // 增值税卷票识别
	// action = "OcrPassInvoice"     // 高速公路过路费票识别
	// action = "OcrFoodProduction"  // 食品生产许可证识别
	// action = "OcrFoodBusiness"    // 食品经营许可证识别
	// action = "OcrClueLicense"     // 营业执照识别
	// action = "OCRTrade"           // 商标证识别
	// action = "OCRRuanzhu"         // 软件著作权识别
	// action = "OCRCosmeticProduct" // 化妆品生产许可证识别
	// action = "OcrSeal"            // 印章识别
	// action = "OcrTextAlignment"   // 合同校验
	// action = "OCRPdf"             // PDF识别
	// action = "OCRTable"           // 表格识别
	// 尝试使用通用文字识别，这个API通常权限要求较低
	action := "OCRNormal"

	fmt.Printf("[火山引擎OCR SDK] 调用API，Action: %s\n", action)

	// 调用SDK
	resp, status, err := visual.DefaultInstance.OCRApi(form, action)
	if err != nil {
		fmt.Printf("[火山引擎OCR SDK] API调用失败 - 错误: %v, 状态码: %d\n", err, status)
		return nil, fmt.Errorf("火山引擎OCR SDK调用失败: %w, 状态: %d", err, status)
	}

	// 检查状态码
	if status != 200 {
		fmt.Printf("[火山引擎OCR SDK] 非200状态码: %d\n", status)
		// 尝试解析错误响应
		if resp != nil {
			respBytes, _ := json.Marshal(resp)
			fmt.Printf("[火山引擎OCR SDK] 错误响应内容: %s\n", string(respBytes))
		}
		return nil, fmt.Errorf("火山引擎OCR SDK返回错误状态码: %d", status)
	}

	// 序列化响应用于调试
	respBytes, _ := json.Marshal(resp)
	fmt.Printf("[火山引擎OCR SDK] 响应内容: %s\n", string(respBytes))

	// 解析响应
	result, err := c.parseSDKResponse(resp, imagePath)
	if err != nil {
		return nil, fmt.Errorf("解析火山引擎OCR SDK响应失败: %w", err)
	}

	fmt.Printf("[火山引擎OCR SDK] 处理完成，置信度: %.2f, 器官名称: %s\n", result.Confidence, result.OrganName)
	return result, nil
}

// parseSDKResponse 解析SDK响应
func (c *VolcEngineSDKOCRClient) parseSDKResponse(resp interface{}, imagePath string) (*OCRResult, error) {
	// 将响应转换为map
	respMap, ok := resp.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("响应格式错误，无法转换为map")
	}

	// 检查响应状态
	if code, exists := respMap["ResponseMetadata"].(map[string]interface{})["Error"]; exists && code != nil {
		errorInfo := code.(map[string]interface{})
		return nil, fmt.Errorf("API返回错误: %v", errorInfo)
	}

	// 获取结果数据
	data, exists := respMap["Result"]
	if !exists {
		return nil, fmt.Errorf("响应中缺少Result字段")
	}

	dataMap, ok := data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("Result字段格式错误")
	}

	// 获取文本行
	lineTexts, exists := dataMap["line_texts"]
	if !exists {
		return nil, fmt.Errorf("响应中缺少line_texts字段")
	}

	textLines, ok := lineTexts.([]interface{})
	if !ok {
		return nil, fmt.Errorf("line_texts字段格式错误")
	}

	// 获取置信度
	lineProbs, _ := dataMap["line_probs"].([]interface{})

	// 构建OCR结果
	result := &OCRResult{
		ImagePath:     imagePath,
		KeyValuePairs: make(map[string]string),
		RawResponse:   []byte(fmt.Sprintf("%v", resp)),
	}

	// 提取文本和置信度
	var totalConfidence float64
	for i, textInterface := range textLines {
		text, ok := textInterface.(string)
		if !ok {
			continue
		}

		// 将文本按索引存储到键值对中
		result.KeyValuePairs[fmt.Sprintf("%d", i)] = text

		// 累加置信度
		if i < len(lineProbs) {
			if prob, ok := lineProbs[i].(float64); ok {
				totalConfidence += prob
			}
		}
	}

	// 计算平均置信度
	if len(textLines) > 0 {
		result.Confidence = totalConfidence / float64(len(textLines))
	}

	// 尝试从文本中提取器官名称（假设第一个文本是器官名称）
	if len(textLines) > 0 {
		if firstText, ok := textLines[0].(string); ok {
			result.OrganName = firstText
			result.KeyValuePairs["0.000"] = firstText // 保持与原有格式兼容
		}
	} else {
		result.OrganName = "未知器官"
	}

	return result, nil
}
